import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Seller On boarding get started screen
class SellerOnBoardingGetStartedScreen extends StatefulWidget {
  final bool isTestStore;
  const SellerOnBoardingGetStartedScreen({Key? key, required this.isTestStore})
      : super(key: key);

  @override
  _SellerOnBoardingGetStartedScreenState createState() =>
      _SellerOnBoardingGetStartedScreenState();
}
// endregion

class _SellerOnBoardingGetStartedScreenState
    extends State<SellerOnBoardingGetStartedScreen> {
  // region Bloc
  late SellerOnBoardingGetStartedBloc sellerOnBoardingGetStartedBloc;

  // endregion

  // region Init
  @override
  void initState() {
    sellerOnBoardingGetStartedBloc =
        SellerOnBoardingGetStartedBloc(context, widget.isTestStore);
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  // region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        verticalSizedBox(70),
        indiaFlag(),
        verticalSizedBox(70),
        bringBusiness(),
        Expanded(child: verticalSizedBox(0)),
        getStarted(),
        readTerms(),
      ],
    );
  }

// endregion

//region India Flag
  Widget indiaFlag() {
    return Image.asset(
      AppImages.appIcon,
      height: 84,
      width: 84,
    );
  }
//endregion

//region Bring your business
  Widget bringBusiness() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 26),
      child: Text(
          "Bring your business online with \nSwadesic at 0% commissions*",
          textAlign: TextAlign.center,
          style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack)),
    );

    // return Text(AppStrings.supportShop,
    //   maxLines: 5,
    //   textAlign: TextAlign.center,
    //   style: TextStyle(
    //       fontSize: 24,
    //       fontWeight: FontWeight.w400,
    //       color: AppColors.appBlack
    //   ),);
  }
//endregion

//region Get Stated
  Widget getStarted() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 37),
      width: double.infinity,
      child: CupertinoButton(
          borderRadius: BorderRadius.circular(100),
          padding: const EdgeInsets.symmetric(vertical: 16.5),
          color: AppColors.brandBlack,
          child: Text(AppStrings.getStarted,
              style: AppTextStyle.access1(textColor: AppColors.appWhite)),
          onPressed: () {
            sellerOnBoardingGetStartedBloc.goToOnBoarding();
          }),
    );
  }
//endregion

//region Read terms and condition
  Widget readTerms() {
    return InkWell(
      onTap: () {
        CommonMethods.opeAppWebView(
            webUrl: AppConstants.appTermsAndConditionSeller, context: context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Text(AppStrings.readTerms,
            style: AppTextStyle.contentText0(
                textColor: AppColors.brandBlack, isUnderline: true)),
      ),
    );
  }
//endregion
}
