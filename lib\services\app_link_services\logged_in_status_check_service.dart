import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_screen.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/services/app_token_service/app_token_service.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/universal_link/comment_link.dart';
import 'package:swadesic/util/universal_link/post_link.dart';
import 'package:swadesic/util/universal_link/product_link.dart';
import 'package:swadesic/util/universal_link/store_and_user_link.dart';
import 'package:swadesic/util/universal_link/universal_link.dart';
// import 'package:uni_links/uni_links.dart';
import 'package:url_launcher/url_launcher.dart';

class LoggedInStatusCheckService{



  //region Constructor
  LoggedInStatusCheckService(){
    //Check if user reference is null or not .
    //If null then assign static user info and continue
    if(AppConstants.appData.userReference == null){
      //Assign static user info and save in cache
      assignStaticUserInfoAndSave();
      //Navigate to user or store bottom navigation
      navigateToUserOrStoreBottomNavigation();
    }
    //Else navigate to user or store bottom navigation
    else{
      //Navigate to user or store bottom navigation
      navigateToUserOrStoreBottomNavigation();
    }
  }
  //endregion




  //region Assign static user info and
  void assignStaticUserInfoAndSave(){
    //Assign static user info in AppConstant and go to Bottom navigation
    AppConstants.appData.userReference = AppConstants.staticUser;
    AppConstants.appData.refreshToken = AppConstants.appCurrentEnvironment == Environment.dev ?AppConstants.staticDevRefreshToken : AppConstants.staticProdRefreshToken;
    AppConstants.appData.refreshExpire = AppConstants.appCurrentEnvironment == Environment.dev ? AppConstants.staticDevRefreshTokenValidity : AppConstants.staticProdRefreshTokenValidity;
    AppConstants.appData.accessToken = AppConstants.appCurrentEnvironment == Environment.dev ? AppConstants.staticDevAccessToken : AppConstants.staticProdAccessToken;
    AppConstants.appData.accessTokenExpire = AppConstants.appCurrentEnvironment == Environment.dev ? AppConstants.staticDevAccessTokenValidity : AppConstants.staticProdAccessTokenValidity;
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Assign initial font
    AppConstants.appData.fontSize = 1.0;
    // //Add all data to share pref
    AppDataService().addAppData();
    }
  //endregion

  //region Navigate to user or store bottom navigation
  void navigateToUserOrStoreBottomNavigation()async{
    //Check auth token
    await AppTokenService().refreshToken();
    //If user view then push to User bottom navigation
    if(AppConstants.appData.isUserView!){
      Navigator.of(AppConstants.globalNavigator.currentContext!).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
      const UserBottomNavigation()), (Route<dynamic> route) => false).then((value) async{
      });
    }
    //Else push to Store bottom navigation
    else{
      Navigator.of(AppConstants.globalNavigator.currentContext!).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
      const StoreBottomNavigation()), (Route<dynamic> route) => false).then((value) async{
      });
    }
  }
//endregion




  //region Clear app data if static user is logged in
  // Future<void> clearAppData()async{
  //   //Get app data
  //   // await AppDataService().getAppData();
  //   // ///Clear the app data if static user info is saved
  //   // if(CommonMethods().isStaticUser()){
  //   //   //Clear data
  //   //   await AppDataService().clearAppDate();
  //   // }
  //   //App is already open
  //   appAlreadyOpen();
  //   //App is closed and app is open
  //   appIsClosed();
  // }
  //endregion




  ///App is closed
  // //region App is closed
  // Future<void> appIsClosed() async {
  //   final appLinks = AppLinks();
  //   if (!AppConstants.isAppOpen) {
  //     //Make is app open to true
  //     AppConstants.isAppOpen = true;
  //     try {
  //       Uri url = await appLinks.getInitialAppLink() ?? Uri.parse("https://google.com");
  //       //print("App closed and open again and url is ${url}");
  //
  //       //Check logged in status
  //       checkLoggedInStatusAndUrl(url: url);
  //     } on PlatformException {
  //       // Platform messages may fail but we ignore the exception
  //       //print('falied to get initial uri');
  //     }
  //   }
  // }
  //
  // //endregion

  ///Already open
//   //region App already open
//   void appAlreadyOpen() {
//     final appLinks = AppLinks();
//     appLinks.allUriLinkStream.listen((Uri? url) async{
//       //else if url contains super_link
//       // if(url!.path.contains("super_link")){
//       //
//       //   //If bottom navigation mounted
//       //   if(AppConstants.userStoreCommonBottomNavigationContext.mounted){
//       //     // Go to super link screen and destroy all previous screen but use the userStoreCommonBottomNavigationContext
//       //     Navigator.of(AppConstants.userStoreCommonBottomNavigationContext).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
//       //     const SuperLink()), (Route<dynamic> route) => false);
//       //   }
//       //   //else use Global navigation
//       //   else {
//       //     // Go to super link screen and destroy all previous screen but use the globalNavigator
//       //     Navigator.of(AppConstants.globalNavigator.currentContext!).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
//       //     const SuperLink()), (Route<dynamic> route) => false);
//       //   }
//       //   return;
//       // }
//
//       //If bottom navigation is not mounted then don't listen
//       if(AppConstants.isBottomNavigationMounted.value){
//         return;
//       }
//
//       //print("App open and Bottom navigation is not mounted yet link is ${url}");
//
//       //Check logged in status
//       checkLoggedInStatusAndUrl(url:url??Uri.parse("https://google.com"));
//       AppConstants.isAppOpen = true;
//
//     }, onError: (Object err) {
//
//       //print(err);
//     });
//   }
//
// //endregion










  ///Handle web app url
  //region Handle app url
  // Future<void> handleWebAppUrl({required Uri url})async{
  //   //Web url
  //   RegExp webUrl = RegExp(r'^(https?:\/\/)?(www\.)?swadesic\.com\/web$');
  //   RegExp productPattern = RegExp(r'^p\?r=P\d+');
  //   RegExp productCommentPattern = RegExp(r'^co/\?s=S[A-Za-z0-9]+&p=P[A-Za-z0-9]+&cId=\d+$');
  //   RegExp postPattern = RegExp(r'^(po|co)\?r=(PO|CO)\d+');
  //   String urlData = '';
  //   try{
  //
  //     await Future.delayed(const Duration(seconds: 5));
  //     ///0. Check is it a web url
  //     if(url.toString().contains("/web")){
  //       //print(url.toString());
  //
  //       CommonMethods.opeAppWebView(webUrl: url.toString(), context: AppConstants.globalNavigator.currentContext!);
  //
  //       // CommonMethods.opeAppWebView(webUrl: url.toString(), context: AppConstants.currentSelectedTabContext);
  //       return;
  //     }
  //
  //
  //         //Check the is the data can be decoyed or not
  //         if(CommonMethods.canDecodeBase32(url.pathSegments.first)){
  //           //Decade data
  //           urlData = CommonMethods.decodeBase32(url.pathSegments.first);
  //
  //
  //       ///1. Check is it a product url
  //       //1. Check the pattern match
  //       if(productPattern.hasMatch(urlData)){
  //         ProductLink(Uri.parse(urlData));
  //       }
  //
  //
  //       ///2. Check is it a comment url
  //       //Check the pattern match
  //       else if(productCommentPattern.hasMatch(urlData)){
  //         CommentLink(Uri.parse(urlData));
  //       }
  //
  //       ///3. Post
  //       //Check the pattern match
  //       else if(postPattern.hasMatch(urlData)){
  //         PostLink(Uri.parse(urlData));
  //       }
  //     }
  //     ///3. Store or user
  //     //Else add the data after / in url Data and push to store and url screen
  //     else{
  //       urlData = url.pathSegments.first;
  //       StoreAndUserLink(url);
  //     }
  //
  //   }
  //   catch (error){
  //     // StoreAndUserLink(url);
  //     //print(error);
  //   }
  // }
//endregion

}