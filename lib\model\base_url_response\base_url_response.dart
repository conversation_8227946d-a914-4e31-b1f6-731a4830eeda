class BaseUrlResponse {
  EnvStructure? dev;
  EnvStructure? qa;
  EnvStructure? prod;
  bool? isMandatoryUpdate;
  String? appVersion;

  BaseUrlResponse(
      {this.dev, this.qa, this.prod, this.isMandatoryUpdate, this.appVersion});

  BaseUrlResponse.fromJson(Map<String, dynamic> json) {
    dev = json['dev'] != null ? new EnvStructure.fromJson(json['dev']) : null;
    qa = json['qa'] != null ? new EnvStructure.fromJson(json['qa']) : null;
    prod = json['prod'] != null ? new EnvStructure.fromJson(json['prod']) : null;
    isMandatoryUpdate = json['is_mandatory_update'];
    appVersion = json['app_version'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.dev != null) {
      data['dev'] = this.dev!.toJson();
    }
    if (this.qa != null) {
      data['qa'] = this.qa!.toJson();
    }
    if (this.prod != null) {
      data['prod'] = this.prod!.toJson();
    }
    data['is_mandatory_update'] = this.isMandatoryUpdate;
    data['app_version'] = this.appVersion;
    return data;
  }
}

class EnvStructure {
  String? baseUrl;
  String? staticAccessToken;
  String? staticRefreshToken;
  String? staticAccessTokenValidity;
  String? staticRefreshTokenValidity;

  EnvStructure(
      {this.baseUrl,
      this.staticAccessToken,
      this.staticRefreshToken,
      this.staticAccessTokenValidity,
      this.staticRefreshTokenValidity});

  EnvStructure.fromJson(Map<String, dynamic> json) {
    baseUrl = json['base_url'];
    staticAccessToken = json['static_access_token'];
    staticRefreshToken = json['static_refresh_token'];
    staticAccessTokenValidity = json['static_access_token_validity'];
    staticRefreshTokenValidity = json['static_refresh_token_validity'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['base_url'] = this.baseUrl;
    data['static_access_token'] = this.staticAccessToken;
    data['static_refresh_token'] = this.staticRefreshToken;
    data['static_access_token_validity'] = this.staticAccessTokenValidity;
    data['static_refresh_token_validity'] = this.staticRefreshTokenValidity;
    return data;
  }
}
