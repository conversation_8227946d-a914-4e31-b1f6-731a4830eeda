import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';
import 'package:swadesic/features/seller/add_image/add_image_screen.dart';
import 'package:swadesic/features/seller/add_image/selected_image_preview/selected_image_preview_screen.dart';
import 'package:swadesic/util/web_file_picker.dart';
import 'package:swadesic/features/seller/labels/labels_screen.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_screen.dart';
import 'package:swadesic/features/seller/product_preview/product_preview_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/seller_return_warranty_response.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class AddProductBloc {
  // region Common Variables
  BuildContext context;
  bool openForOrder = false;
  final int storeId;

  // late AddProductData addProductData;
  final String storeReference;

  ///Delivery settings
  late SellerDeliveryStoreResponse addProductLevelDeliverySettingResponse =
      SellerDeliveryStoreResponse();

  // final StoreInfo storeInfo;
  // bool isReturnConditionAdded = false;

  ///Return warranty
  late SellerReturnWarrantyResponse addProductLevelReturnSettingresponse =
      SellerReturnWarrantyResponse();

  ///Add product

  late Product product = Product();

  ///Region store dashboard
  late StoreDashboardService storeDashboardService = StoreDashboardService();

  ///Store info data model
  late SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel;

  // endregion

  //region Scroll Controller

  //endregion

  //region Color change Text editing controller

  // TextEditingController commentTextFieldCtrl = TextEditingController();
  //endregion

  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  final optionsRefreshCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  AddProductBloc(this.context, this.storeId, this.storeReference);

  // endregion

  // region Init
  void init() {
    // addProductData = AddProductData();
    addEditFieldClear();
    clearImages();
    getStoreDashboard();
  }

// endregion

  //region Get store dashboard
  getStoreDashboard() async {
    /// Reference to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    // Store context in a local variable to avoid BuildContext across async gaps warning
    final currentContext = context;

    try {
      StoreDashboardResponse storeDashboardResponse =
          await storeDashboardService.getStoreDashboard(
              storeReference: storeReference);
      //Add data into data model
      storeDashboardDataModel.addDashboard(data: storeDashboardResponse.data!);
      //Refresh options
      optionsRefreshCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(
            AppStrings.commonErrorMessage, currentContext);
      }
      return;
    } catch (error) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(
            AppStrings.commonErrorMessage, currentContext);
      }
      return;
    }
  }

  //endregion

  //region Clear images
  void clearImages() {
    AppConstants.multipleSelectedImage.clear();
  }

  //endregion

  //region Add edit field
  addEditFieldClear() {
    AddEditProductFieldsBloc.brandNameTextCtrl.clear();
    AddEditProductFieldsBloc.productNameTextCtrl.clear();
    AddEditProductFieldsBloc.productCategoryTextCtrl.clear();
    AddEditProductFieldsBloc.productDescNameTextCtrl.clear();
    AddEditProductFieldsBloc.promoLinkTextCtrl.clear();
    // AddEditProductFieldsBloc.inStockTextCtrl.clear(); // Moved to inventory options
    AddEditProductFieldsBloc.hashTagsTextCtrl.clear();
    // AddEditProductFieldsBloc.mrpTextCtrl.clear(); // Moved to inventory options
    // AddEditProductFieldsBloc.sellingPriceTextCtrl.clear(); // Moved to inventory options
    AddEditProductFieldsBloc.isUrlValid = null;
    AddEditProductFieldsBloc.productSlugTextCtrl.clear();
    AddEditProductFieldsBloc.productCodeTextCtrl.clear();
  }

  //endregion

  //region On tap back
  void onTapBack() {
    //Check is anything change
    if (AddEditProductFieldsBloc.brandNameTextCtrl.text.isNotEmpty ||
        AddEditProductFieldsBloc.productNameTextCtrl.text.isNotEmpty ||
        AddEditProductFieldsBloc.productCategoryTextCtrl.text.isNotEmpty ||
        AddEditProductFieldsBloc.productDescNameTextCtrl.text.isNotEmpty ||
        AddEditProductFieldsBloc.promoLinkTextCtrl.text.isNotEmpty ||
        // AddEditProductFieldsBloc.inStockTextCtrl.text.isNotEmpty || // Moved to inventory options
        // AddEditProductFieldsBloc.mrpTextCtrl.text.isNotEmpty || // Moved to inventory options
        // AddEditProductFieldsBloc.sellingPriceTextCtrl.text.isNotEmpty || // Moved to inventory options
        AddEditProductFieldsBloc.productSlugTextCtrl.text.isNotEmpty ||
        AddEditProductFieldsBloc.productCodeTextCtrl.text.isNotEmpty) {
      openDialog();
    } else {
      Navigator.pop(context);
    }
  }

//endregion

  //region Open dialog
  openDialog() {
    CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          onTapSave: (value) {
            Navigator.pop(context);
            // Navigator.pop(context);
          },
          firstButtonName: "Go back",
          secondButtonName: "Stay",
          popPreviousScreen: false,
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.youWillLooseAllTheDetails,
        ));
  }

  //endregion

  //region Go To Select Image Screen
  void goToAddImage() async {
    // For web platform
    if (kIsWeb) {
      try {
        // Get app config data model for image limit
        final appConfigDataModel =
            Provider.of<AppConfigDataModel>(context, listen: false);
        int imageLimit = appConfigDataModel.appConfig!.productImageLimit;

        // Check if we've already reached the image limit
        if (AppConstants.webProductImages.length >= imageLimit) {
          // Store context in a local variable to avoid BuildContext across async gaps warning
          final currentContext = context;
          if (currentContext.mounted) {
            return CommonMethods.toastMessage(
                "${AppStrings.youCantAddMoreThen} $imageLimit images",
                currentContext);
          }
          return;
        }

        // Use WebFilePicker to pick multiple images
        final imageDataList = await WebFilePicker.pickMultipleImages();
        if (imageDataList != null && imageDataList.isNotEmpty) {
          // No need to check if webProductImages is null since it's initialized as an empty list

          // Check if adding these images would exceed the limit
          int currentCount = AppConstants.webProductImages.length;
          if (currentCount + imageDataList.length > imageLimit) {
            // Calculate how many images we can add
            int availableSlots = imageLimit - currentCount;
            if (availableSlots > 0) {
              // Add only the number of images that fit within the limit
              AppConstants.webProductImages
                  .addAll(imageDataList.take(availableSlots));
            }

            // Store context in a local variable to avoid BuildContext across async gaps warning
            final currentContext = context;
            if (currentContext.mounted) {
              return CommonMethods.toastMessage(
                  "${AppStrings.youCantAddMoreThen} $imageLimit images",
                  currentContext);
            }
            return;
          }

          // Add all selected images to the list
          AppConstants.webProductImages.addAll(imageDataList);

          // Update UI
          imageCtrl.sink.add(true);
        }
      } catch (e) {
        debugPrint("Error picking web images: $e");
      }
      return;
    }

    // For mobile platform
    var screen = const AddImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((_) {
      if (AppConstants.multipleSelectedImage.isEmpty) {
        return imageCtrl.sink.add(false);
      }
      imageCtrl.sink.add(true);
    });
  }

  //endregion

  //region Go to Selected Image Preview Screen
  void goToSelectedImageScreen() {
    CommonMethods.closeKeyboard(context);

    // For web platform, we don't navigate to the preview screen
    // Instead, we just update the UI to show the selected images
    if (kIsWeb) {
      imageCtrl.sink.add(true);
      return;
    }

    // For mobile platform
    var screen = const SelectedImagePreviewScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((_) {
      if (AppConstants.multipleSelectedImage.isEmpty) {
        return imageCtrl.sink.add(false);
      }
      imageCtrl.sink.add(true);
    });
  }

  //endregion

  /// Go to Delivery settings
  //region Go to Deliver setting
  void goToDeliverSettingScreen() {
    var screen = SellerStoreDeliverySettingScreen(
      storeRef: storeReference,
      isFromAddProduct: true,
      isFromStore: false,
      isFromEditProduct: false,
      deliverySettingsDataFromAddProduct:
          addProductLevelDeliverySettingResponse.message == null
              ? null
              : addProductLevelDeliverySettingResponse,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //Get dashboard data
      getStoreDashboard();
      //If data is not null then add data to delivery response
      if (value != null) {
        // addProductLevelDeliverySettingResponse = value;
        // Copy values from `value` to `addProductLevelDeliverySettingResponse`
        addProductLevelDeliverySettingResponse = SellerDeliveryStoreResponse(
            message: 'success',
            deliverySettingData:
                DeliverySettingData.copy(value.deliverySettingData),
            saveAsStoreDefault: value
                .saveAsStoreDefault); // Preserve the saveAsStoreDefault flag

        CommonMethods.toastMessage(
            AppStrings.yourDeliverySettingForThisProductIsSaved, context,
            toastShowTimer: 3);
        //Refresh label
        optionsRefreshCtrl.sink.add(true);
      } else {
        return;
      }
      //print(value);
    });
  }

  //endregion

  ///Go to return and warranty
  //region Go to return and warranty
  void goToReturnAndWarranty() async {
    var screen = SellerReturnStoreWarrantyScreen(
      storeRef: storeReference,
      isFromAddProduct: true,
      fromEditProductScreen: false,
      fromStoreScreen: false,
      fromProductScreen: false,
      returnSettingsDataFromAddProduct:
          addProductLevelReturnSettingresponse.message == null
              ? null
              : addProductLevelReturnSettingresponse,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //Get dashboard data
      getStoreDashboard();
      //If data is not null then add data to delivery response
      if (value != null) {
        addProductLevelReturnSettingresponse = value;
        CommonMethods.toastMessage(
            AppStrings.yourReturnSettingForThisProductIsSaved, context,
            toastShowTimer: 3);
        //Refresh label
        optionsRefreshCtrl.sink.add(true);
      } else {
        return;
      }
    });
  }

//endregion

  ///Go to inventory options screen
  //region Go to inventory options
  void goToInventoryOptions({required String storeReference}) async {
    var screen = InventoryOptionsScreen(
      storeReference: storeReference,
      product: product,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value != null) {
        //If it has data
        if (value != null) {
          CommonMethods.toastMessage(
              AppStrings.inventoryOptionsUpdated, context,
              toastShowTimer: 3);
          //Refresh options
          optionsRefreshCtrl.sink.add(true);
        }
      } else {
        return;
      }
    });
  }

//endregion

  ///Go to label screen
  //region Go to labels
  void goToLabels({required String storeReference}) async {
    var screen = LabelsScreen(
      isFromTrustCenter: false,
      storeReference: storeReference,
      product: product,
      brand: product.swadeshiBrand,
      owned: product.swadeshiOwned,
      made: product.swadeshiMade,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value != null) {
        //If it has data
        if (value != null) {
          CommonMethods.toastMessage(
              AppStrings.yourLabelsForThisProductIsSaved, context,
              toastShowTimer: 3);
          //Refresh label
          optionsRefreshCtrl.sink.add(true);
        }
      } else {
        return;
      }
    });
  }

//endregion

  //region All product data to product model
  void addProductDataToProductModel() {
    // Retrieve the data from the StoreInfoModel
    sellerOwnStoreInfoDataModel =
        Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);

    try {
      product.brandName = AddEditProductFieldsBloc.brandNameTextCtrl.text;
      // product.productReference = "Px00000000";

      // product.storehandle = "Product Preview";
      product.productName = AddEditProductFieldsBloc.productNameTextCtrl.text;
      product.productSlug = AddEditProductFieldsBloc.productSlugTextCtrl.text;
      product.productCode = AddEditProductFieldsBloc.productCodeTextCtrl.text;
      product.productCategory =
          AddEditProductFieldsBloc.productCategoryTextCtrl.text;
      product.productDescription =
          AddEditProductFieldsBloc.productDescNameTextCtrl.text;
      product.promotionLink = AddEditProductFieldsBloc.promoLinkTextCtrl.text;
      product.storeReference =
          sellerOwnStoreInfoDataModel.storeInfo!.storeReference!;
      product.storehandle = sellerOwnStoreInfoDataModel.storeInfo!.storehandle!;
      product.productReference = "New";
      product.hashTag = AddEditProductFieldsBloc.hashTagsTextCtrl.text;

      // Note: inStock, mrpPrice, and sellingPrice are now handled in inventory options
      // They will be set from variants or no-variant data when inventory options are saved

      product.targetGender = AddEditProductFieldsBloc.gender;

      product.swadeshiOwned =
          sellerOwnStoreInfoDataModel.storeInfo!.swadeshiOwned!;
      product.likeCount = 56;

      //Add sold
      product.ordersCount = 31;
      //return count
      product.returnCount = 1;
      //Like status
      product.likeStatus = false;
      //Store image
      product.storeIcon = "";
      //Add comment
      product.commentCount = 19;
      //Created date
      final now = DateTime.now();
      final formatter = DateFormat('dd:MM:yyyy HH:mm:ss');
      product.createdDate = formatter.format(now);
      //App version
      product.productVersion = "1.0.0";
      //Updated date
      product.updatedDate = formatter.format(now);
      //Saved or not
      product.saveStatus = true;
      //Is buy enable
      product.isBuyEnable = true;
      //Product status message
      product.productStatusMessage = "";
      //Delivery by
      product.deliveryBy = 1;
      //Store icon
      product.storeIcon = sellerOwnStoreInfoDataModel.storeInfo!.icon;
      //Add images
      // for(var image in AppConstants.multipleSelectedImage){
      //
      //
      //   product.prodImages = []..add(ProdImages(productImage:image.path,createdBy: "",modifiedBy: "hj",productid: 1,productimageid: 1,reorder: 0));
      //
      //
      // }
      //Clear images
      if (product.prodImages != null) {
        product.prodImages!.clear();
      }
      product.prodImages = [];

      if (kIsWeb) {
        // For web platform, use webProductImages
        for (var image in AppConstants.webProductImages) {
          product.prodImages!.add(
            ProdImages(
              productImage: image['name'], // Use the file name for preview
              createdBy: "",
              modifiedBy: "hj",
              productid: 1,
              productimageid: 1,
              reorder: 0,
            ),
          );
        }
      } else {
        // For mobile platform, use multipleSelectedImage
        for (var image in AppConstants.multipleSelectedImage) {
          product.prodImages!.add(
            ProdImages(
              productImage: image.path,
              createdBy: "",
              modifiedBy: "hj",
              productid: 1,
              productimageid: 1,
              reorder: 0,
            ),
          );
        }
      }

      //print(product.prodImages);
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  //endregion

  //region go to Add Product Preview Screen

  void goToAddProductPreview() {
    //Brand name
    if (AddEditProductFieldsBloc.brandNameTextCtrl.text.trim().isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.brandNameCanNotBeEmpty, context);
    }
    //Product name
    if (AddEditProductFieldsBloc.productNameTextCtrl.text.trim().isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.productNameCanNotBeEmpty, context);
    }
    //Product slug
    if (AddEditProductFieldsBloc.productSlugTextCtrl.text.trim().isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.productSlugCanNotBeEmpty, context);
    }
    //Product category
    if (AddEditProductFieldsBloc.productCategoryTextCtrl.text.trim().isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.productCategoryCanNotBeEmpty, context);
    }
    //Product description
    if (AddEditProductFieldsBloc.productDescNameTextCtrl.text.trim().isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.productDescriptionCanNotBeEmpty, context);
    }
    //Check url
    if (AddEditProductFieldsBloc.promoLinkTextCtrl.text.isNotEmpty &&
        !AddEditProductFieldsBloc.isUrlValid!) {
      CommonMethods.toastMessage(
          AppStrings.pleaseEnterValidPromotionLink, context);
      return;
    }
    // //Stocks
    // if (AddEditProductFieldsBloc.inStockTextCtrl.text.trim().isEmpty ||
    //     int.parse(AddEditProductFieldsBloc.inStockTextCtrl.text) <= 0) {
    //   return CommonMethods.toastMessage(
    //       AppStrings.stockQuantityCanNotBeEmptyOrZero, context);
    // }
    // //MRP
    // if (AddEditProductFieldsBloc.mrpTextCtrl.text.trim().isEmpty ||
    //     int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text) <= 0) {
    //   return CommonMethods.toastMessage(
    //       AppStrings.mrpCanNotBeEmptyOrZero, context);
    // }
    // //Selling price
    // if (AddEditProductFieldsBloc.sellingPriceTextCtrl.text.trim().isEmpty) {
    //   return CommonMethods.toastMessage(
    //       AppStrings.sellingPriceCanNotBeEmptyOrZero, context);
    // }
    // //If selling price should not be greater then MRP
    // if (int.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text) >
    //     int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text)) {
    //   CommonMethods.toastMessage(
    //       AppStrings.sellingPriceShouldNotBeMoreThenMRP, context);
    //   return;
    // }
    //Check swadesic labels
    if (product.swadeshiMade == null ||
        product.swadeshiBrand == null ||
        product.swadeshiOwned == null) {
      CommonMethods.toastMessage(
          AppStrings.pleaseAddSwadeshiLabelForThisProduct, context);
      return;
    }
    //Delivery setting status is false(If both are false)
    if (!checkDeliverySettingStatus()) {
      CommonMethods.toastMessage(
          AppStrings.pleaseProvideDeliveryOrReturnSetting, context,
          toastShowTimer: 5);
      return;
    }
    //Return setting status is false(If both are false)
    if (!checkReturnSettingStatus()) {
      CommonMethods.toastMessage(
          AppStrings.pleaseProvideDeliveryOrReturnSetting, context,
          toastShowTimer: 5);
      return;
    }
    //Product image check
    if (kIsWeb) {
      // For web platform
      if (AppConstants.webProductImages.isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.pleaseAddAtLeaseOneProductImage, context);
        return;
      }
    } else {
      // For mobile platform
      if (AppConstants.multipleSelectedImage.isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.pleaseAddAtLeaseOneProductImage, context);
        return;
      }
    }

    addProductDataToProductModel();

    var screen = ProductPreviewScreen(
        storeId: storeId,
        storeReference: storeReference,
        sellerDeliveryStoreResponse: addProductLevelDeliverySettingResponse,
        returnSetting: addProductLevelReturnSettingresponse,
        product: product);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Check delivery setting status
  /*
  If store level setting or product level setting any of them is true then return true.
  Else return false.
   */
  bool checkDeliverySettingStatus() {
    /// Retrieve to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    bool status = false;
    //If has product level setting
    if (addProductLevelDeliverySettingResponse.deliverySettingData != null) {
      status = true;
    }
    //If store level setting
    else if (storeDashboardDataModel.storeDashBoard.deliverySettings!) {
      status = true;
    }
    //If both are false
    else {
      status = false;
    }
    return status;
  }

  //endregion

  //region Check return setting status
  /*
  If store level setting or product level setting any of them is true then return true.
  Else return false.
   */
  bool checkReturnSettingStatus() {
    /// Retrieve to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    bool status = false;
    //If has product level setting
    if (addProductLevelReturnSettingresponse.data != null) {
      status = true;
    }
    //If store level setting
    else if (storeDashboardDataModel.storeDashBoard.warrantyAndReturn!) {
      status = true;
    }
    //If both are false
    else {
      status = false;
    }
    return status;
  }

  //endregion

  //region Check delivery and return settings
  bool checkDeliveryAndReturnSetting() {
    /// Retrieve to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    //Check is delivery and return setting are selected
    /*
    1. Check is any product level setting is selected or not. If yes then don't give any warning.
    2. If store level setting are there then return.
    2. Else show the message that "You are missing delivery or return settings"
     */
    //If product level setting is selected
    if (addProductLevelDeliverySettingResponse.deliverySettingData != null &&
        addProductLevelReturnSettingresponse.data != null) {
      return true;
    }
    //If already have store level setting then return
    if (storeDashboardDataModel.storeDashBoard.deliverySettings! &&
        storeDashboardDataModel.storeDashBoard.warrantyAndReturn!) {
      return true;
    }
    //Else show that you are missing the delivery/return setting
    return false;
  }

  //endregion

  //region Go to tag stories
  void goToTagStories() {}
  //endregion

//region Dispose
  void dispose() {
    imageCtrl.close();
    optionsRefreshCtrl.close();
    AppConstants.multipleSelectedImage.clear();
    AppConstants.webProductImages.clear();
  }
//endregion
}
