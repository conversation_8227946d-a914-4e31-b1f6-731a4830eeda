class CreateOrderAndInitiatePaymentResponse {
  String? message;
  bool? isProfileComplete;
  //If some store is closed
  List<int>? closedStore;
  //If some store is deleted
  List<int>? deletedStore;
  //Deleted product but store is open for order
  List<String>? deletedProducts;
  //Not deliverable
  List<String>? notDeliverable;
  //Onlypickupproducts
  List<String>? onlyPickupProducts;
  //Out of stock products
  List<String>? outOfStockProducts;
  PaymentDetails? paymentDetails;
  String? webUrl;

  CreateOrderAndInitiatePaymentResponse({
    this.isProfileComplete,
    this.closedStore,this.deletedStore,this.deletedProducts,
    this.notDeliverable,this.message,
    this.paymentDetails,
    this.onlyPickupProducts,
    this.outOfStockProducts,
    this.webUrl
  });

  CreateOrderAndInitiatePaymentResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    webUrl = json['html_short_url'];
    isProfileComplete = json['is_profile_complete']??false;
    ///Check if close store is null
    closedStore = json['closed_store'] == null? []:json['closed_store'].cast<int>();
    ///Check if deleted store is null
    deletedStore = json['deleted_store'] == null? []:json['deleted_store'].cast<int>();
    ///Check if deleted product is null
    deletedProducts = json['deleted_product'] == null? []:json['deleted_product'].cast<String>();
    ///Check if non_deliverable_products is null
    notDeliverable = json['non_deliverable_products'] == null? []:json['non_deliverable_products'].cast<String>();
    ///check if only_pickup_products is null
    onlyPickupProducts = json['only_pickup_products'] == null? []:json['only_pickup_products'].cast<String>();

    paymentDetails = json['payment_details'] != null
        ? new PaymentDetails.fromJson(json['payment_details'])
        : null;
    outOfStockProducts = json['out_of_stock_products'] == null? []:json['out_of_stock_products'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['is_profile_complete'] = this.isProfileComplete;
    //If some store is closed
    data['closed_store'] = closedStore;
    //If some store is deleted
    data['deleted_store'] = deletedStore;
    //If some product are deleted
    data['deleted_product'] = deletedProducts;
    //If not not Deliverable
    data['non_deliverable_products'] = notDeliverable;
    if (this.paymentDetails != null) {
      data['payment_details'] = this.paymentDetails!.toJson();
    }
    data['only_pickup_products'] = onlyPickupProducts;
    data['out_of_stock_products'] = outOfStockProducts;
    return data;
  }
}

class PaymentDetails {
  String? key;
  String? amount;
  String? currency;
  String? name;
  String? description;
  String? image;
  String? orderId;
  Prefill? prefill;
  Notes? notes;
  Theme? theme;

  PaymentDetails(
      {this.key,
        this.amount,
        this.currency,
        this.name,
        this.description,
        this.image,
        this.orderId,
        this.prefill,
        this.notes,
        this.theme});

  PaymentDetails.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    amount = json['amount'].toString();
    currency = json['currency'];
    name = json['name'];
    description = json['description'];
    image = json['image'];
    orderId = json['order_id'];
    prefill =
    json['prefill'] != null ? new Prefill.fromJson(json['prefill']) : null;
    notes = json['notes'] != null ? new Notes.fromJson(json['notes']) : null;
    theme = json['theme'] != null ? new Theme.fromJson(json['theme']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['key'] = this.key;
    data['amount'] = this.amount;
    data['currency'] = this.currency;
    data['name'] = this.name;
    data['description'] = this.description;
    data['image'] = this.image;
    data['order_id'] = this.orderId;
    if (this.prefill != null) {
      data['prefill'] = this.prefill!.toJson();
    }
    if (this.notes != null) {
      data['notes'] = this.notes!.toJson();
    }
    if (this.theme != null) {
      data['theme'] = this.theme!.toJson();
    }
    return data;
  }
}

class Prefill {
  String? name;
  String? email;
  String? contact;

  Prefill({this.name, this.email, this.contact});

  Prefill.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    email = json['email'];
    contact = json['contact'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['email'] = this.email;
    data['contact'] = this.contact;
    return data;
  }
}

class Notes {
  String? orderRequestNumber;
  String? txnToken;

  Notes({this.orderRequestNumber, this.txnToken});

  Notes.fromJson(Map<String, dynamic> json) {
    orderRequestNumber = json['order_request_number'];
    txnToken = json['txn_token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['order_request_number'] = this.orderRequestNumber;
    data['txn_token'] = this.txnToken;
    return data;
  }
}

class Theme {
  String? color;

  Theme({this.color});

  Theme.fromJson(Map<String, dynamic> json) {
    color = json['color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['color'] = this.color;
    return data;
  }
}



