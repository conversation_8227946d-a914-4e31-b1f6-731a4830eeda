import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/delivery_service_location/delivery_service_location.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/logistics_partner_response/logistics_partner_response.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_pin_code_response.dart'
    as pin_code;
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/address_response.dart'
    as address;
import 'package:swadesic/services/logistics_partner_services/logistics_partner_services.dart';
import 'package:swadesic/services/seller_settings_services/seller_delivery_settings_service.dart';
import 'package:swadesic/services/seller_settings_services/seller_return_warranty_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/pickup_location/pickup_location.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/pickup_location/add_pickup_location_screen.dart';

enum PinCodeCheck { AllSelected, NoneSelected, PartiallySelected }

enum PinCodeState { Loading, Success, Failed, Empty }

enum DeliverySettingState { Loading, Success, Failed, Empty }

class SellerStoreDeliverySettingBloc {
  // region Common Variables
  BuildContext context;
  int allStateSelected = 0;
  final String storeRef;
  final int? productId;
  final String? productReference;
  final bool isFromAddProduct;

  ///Default store

  bool saveAsStoreDefault = false;
  bool resetVisible = false;

  ///Time to prepare time to deliver
  int timeToPrepare = 0;
  int timeToDeliver = 0;
  bool isSettingAvailable = false;

  ///Delivery PinCode
  late pin_code.SellerDeliveryLocationPinCodeResponse
      sellerDeliveryLocationPinCodeResponse =
      pin_code.SellerDeliveryLocationPinCodeResponse();

  ///Add and Get Delivery Settings Api Service
  late SellerDeliverySettingsService sellerDeliverySettingsService;
  static late SellerDeliveryStoreResponse productLevelDeliverySettings;
  static late SellerDeliveryStoreResponse storeLevelDeliverySettingResponse;
  final SellerDeliveryStoreResponse addProductLevelDeliverySettings;

  ///Logistics partners

  late LogisticsPartnerServices logisticsPartnerServices;
  late LogisticsPartnerResponse logisticsPartnerResponse;

  ///Pickup locations
  late ValueNotifier<List<address.Data>> pickupLocationsNotifier;
  List<address.Data> pickupLocations = [];

  // endregion

  //region Controller

  final deliverySettingCtrl =
      StreamController<DeliverySettingState>.broadcast();
  final isSameSettingCtrl = StreamController<bool>.broadcast();

  ///Refresh Pincode ctrl
  final pinCodeCtrl = StreamController<bool>.broadcast();
  final pinCodeStateCtrl = StreamController<PinCodeState>.broadcast();

  //endregion

  // region | Constructor |
  SellerStoreDeliverySettingBloc(
      this.context,
      this.storeRef,
      this.productId,
      this.productReference,
      this.isFromAddProduct,
      this.addProductLevelDeliverySettings) {
    this.context = context;
    pickupLocationsNotifier = ValueNotifier<List<address.Data>>([]);
    sellerDeliverySettingsService = SellerDeliverySettingsService();
    logisticsPartnerServices = LogisticsPartnerServices();
  }

  // endregion

  // region Init
  Future<void> init() async {
    try {
      ///Store
      await getStoreLevelDeliverySettings();

      ///Product level
      await getProductLevelDeliverySetting();

      ///Load pickup locations after delivery settings are loaded
      await loadPickupLocations();

      ///Logistics partner
      getLogisticsPartner();
      deliverySettingCtrl.add(DeliverySettingState.Success);
    } catch (e) {
      //print("Error in init: $e");
      deliverySettingCtrl.add(DeliverySettingState.Failed);
    }
  }

  Future<void> loadPickupLocations() async {
    try {
      //print("Fetching pickup locations for store: $storeRef");
      final service = SellerReturnWarrantySettingsService();
      final response = await service.getPickupLocations(storeRef);

      //print("Got response: $response");
      if (response.data != null && response.data!.isNotEmpty) {
        //print("Pickup locations response: ${response.data!.length} locations");

        // Determine which locations should be checked
        final deliverySettings = getCurrentDeliverySettings();
        final selectedPickupLocationIds =
            deliverySettings?.pickupLocationIds ?? [];

        // Update pickup locations based on selected location IDs
        pickupLocations = response.data!.map((location) {
          // Check if this location's ID is in the selected pickup location IDs
          location.isPickupLocation =
              selectedPickupLocationIds.contains(location.addressid.toString());
          return location;
        }).toList();

        // Update product-level settings with selected pickup location IDs
        productLevelDeliverySettings.deliverySettingData?.pickupLocationIds =
            pickupLocations
                .where((loc) => loc.isPickupLocation ?? false)
                .map((loc) => loc.addressid.toString())
                .toList();

        pickupLocationsNotifier.value = pickupLocations;
      } else {
        //print("No pickup locations found");
        pickupLocations = [];
        pickupLocationsNotifier.value = [];

        // Clear pickup location IDs if no locations found
        productLevelDeliverySettings.deliverySettingData?.pickupLocationIds =
            [];
      }
    } catch (e) {
      //print("Error loading pickup locations: $e");
      pickupLocations = [];
      pickupLocationsNotifier.value = [];

      // Clear pickup location IDs in case of error
      productLevelDeliverySettings.deliverySettingData?.pickupLocationIds = [];
    }
  }

  void togglePickupLocationStatus(int index, bool status) {
    final location = pickupLocations[index];
    location.isPickupLocation = status;
    pickupLocationsNotifier.value = List.from(pickupLocations);

    // Update product-level settings with selected pickup location IDs
    productLevelDeliverySettings.deliverySettingData?.pickupLocationIds =
        pickupLocations
            .where((loc) => loc.isPickupLocation ?? false)
            .map((loc) => loc.addressid.toString())
            .toList();

    // Trigger settings change when pickup location status is modified
    onChangeSetting();
  }

  void addPickupLocation() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddPickupLocationScreen(),
      ),
    );

    if (result != null && result is PickupLocation) {
      try {
        final service = SellerReturnWarrantySettingsService();
        final newLocation = await service.addPickupLocation(
          storeRef: storeRef,
          address: result.address,
          city: result.city,
          pinCode: result.pincode,
          state: result.state,
          name: result.locationName,
          phoneNumber: result.contactNumber,
          latitude: result.latitude,
          longitude: result.longitude,
          locationLink: result.locationLink,
          pickupTimings: {"Mon": "9:00 AM to 9:00 PM"},
          storeAddressType: result.storeAddressType,
        );

        // Refresh pickup locations after add
        await loadPickupLocations();

        // Trigger settings change when a new pickup location is added
        onChangeSetting();
      } catch (e) {
        //print("Error adding pickup location: $e");
      }
    }
  }

  void editPickupLocation(address.Data location) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddPickupLocationScreen(
          pickupLocation: PickupLocation(
            locationName: location.name ?? '',
            address: location.address ?? '',
            city: location.city ?? '',
            state: location.state ?? '',
            pincode: location.pincode ?? '',
            latitude: location.latitude ?? '0',
            longitude: location.longitude ?? '0',
            locationLink: location.locationLink ?? '',
            contactNumber: location.phoneNumber ?? '',
            isActive: location.isPickupLocation ?? true,
          ),
        ),
      ),
    );

    if (result != null && result is PickupLocation) {
      try {
        final service = SellerReturnWarrantySettingsService();
        await service.editPickupLocation(
          addressId: location.addressid!,
          storeRef: storeRef,
          address: result.address,
          city: result.city,
          pinCode: result.pincode,
          state: result.state,
          name: result.locationName,
          phoneNumber: result.contactNumber,
          latitude: result.latitude,
          longitude: result.longitude,
          isPickupLocation: true,
          locationLink: result.locationLink,
          pickupTimings: {"Mon": "9:00 AM to 9:00 PM"},
          storeAddressType: result.storeAddressType,
        );

        // Refresh pickup locations after edit
        await loadPickupLocations();

        // Trigger settings change when a pickup location is edited
        onChangeSetting();
      } catch (e) {
        //print("Error updating pickup location: $e");
      }
    }
  }

  void deletePickupLocation(address.Data location) async {
    try {
      final service = SellerReturnWarrantySettingsService();
      final success = await service.deletePickupLocation(location.addressid!);

      if (success) {
        // Update local state after successful deletion
        await loadPickupLocations(); // Refresh the entire list from server
        onChangeSetting();
      }
    } catch (e) {
      //print("Error deleting pickup location: $e");
    }
  }

  // endregion

  // region On change setting
  void onChangeSetting() {
    // Log current product and store level settings
    final productLevelSettings =
        productLevelDeliverySettings.deliverySettingData;
    final storeLevelSettings =
        storeLevelDeliverySettingResponse.deliverySettingData;

    // Check if settings are the same
    final areSame = isSameSetting();

    // Set resetVisible flag for add product flow
    // resetVisible should be true when settings are different (customized)
    // resetVisible should be false when settings are same (fetched from store)
    resetVisible = !areSame;

    // Add to stream
    isSameSettingCtrl.add(!areSame);
  }

  //endregion

  //region Is same setting
  bool isSameSetting() {
    final productLevelSettings =
        productLevelDeliverySettings.deliverySettingData;
    final storeLevelSettings =
        storeLevelDeliverySettingResponse.deliverySettingData;

    // If no product level settings exist, they are considered the same
    if (productLevelSettings?.deliverysettingid == 0) {
      //print('No product level settings, returning true');
      return true;
    }

    // Compare pickup location IDs
    final productPickupLocations =
        productLevelSettings?.pickupLocationIds ?? [];
    final storePickupLocations = storeLevelSettings?.pickupLocationIds ?? [];
    final pickupLocationsMatch =
        _areListsEqual(productPickupLocations, storePickupLocations);

    // Comprehensive comparison of key settings
    final isSame = productLevelSettings?.fulfillmentOptions ==
            storeLevelSettings?.fulfillmentOptions &&
        productLevelSettings?.deliverymethodWhitelabel ==
            storeLevelSettings?.deliverymethodWhitelabel &&
        productLevelSettings?.deliverymethodLogistics ==
            storeLevelSettings?.deliverymethodLogistics &&
        productLevelSettings?.deliverymethodSelf ==
            storeLevelSettings?.deliverymethodSelf &&
        productLevelSettings?.timeToPrepare ==
            storeLevelSettings?.timeToPrepare &&
        productLevelSettings?.timeToDeliver ==
            storeLevelSettings?.timeToDeliver &&
        productLevelSettings?.deliveryPersonalName ==
            storeLevelSettings?.deliveryPersonalName &&
        productLevelSettings?.deliveryPersonalPhone ==
            storeLevelSettings?.deliveryPersonalPhone &&
        productLevelSettings?.deliveryLocations ==
            storeLevelSettings?.deliveryLocations &&
        productLevelSettings?.deliveryfeeValuetype ==
            storeLevelSettings?.deliveryfeeValuetype &&
        productLevelSettings?.deliveryfeeValue ==
            storeLevelSettings?.deliveryfeeValue &&
        productLevelSettings?.deliveryfeetypeAllFree ==
            storeLevelSettings?.deliveryfeetypeAllFree &&
        productLevelSettings?.deliveryfeetypeStandard ==
            storeLevelSettings?.deliveryfeetypeStandard &&
        productLevelSettings?.defaultLogisticPartner ==
            storeLevelSettings?.defaultLogisticPartner &&
        pickupLocationsMatch;

    // Detailed logging of differences
    if (!isSame) {
      //print('Settings are different:');
      if (!pickupLocationsMatch) {
        //print('Pickup Locations: $productPickupLocations != $storePickupLocations');
      }
    }

    return isSame;
  }

  // Helper method to compare lists
  bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;

    // Sort lists to ensure consistent comparison
    final sortedList1 = List<String>.from(list1)..sort();
    final sortedList2 = List<String>.from(list2)..sort();

    // Compare each element
    for (int i = 0; i < sortedList1.length; i++) {
      if (sortedList1[i] != sortedList2[i]) return false;
    }

    return true;
  }

  // endregion

  ///Store level delivery setting
  //region Get Store level Delivery Store Settings API Call
  Future<void> getStoreLevelDeliverySettings() async {
    //region Try
    try {
      // deliverySettingCtrl.sink.add(DeliverySettingState.Loading);
      storeLevelDeliverySettingResponse = await sellerDeliverySettingsService
          .getDeliveryStoreSettings(storeRef: storeRef, productReference: null);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      deliverySettingCtrl.add(DeliverySettingState.Failed);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      deliverySettingCtrl.add(DeliverySettingState.Failed);

      return;
    }
  }

  //endregion

  ///Product level delivery settings
  //region Get Product level Delivery  Settings API Call
  Future<void> getProductLevelDeliverySetting() async {
    // productLevelDeliverySettings = await sellerDeliverySettingsService.getDeliveryStoreSettings(storeRef: storeRef, productReference: productReference);
    //
    // return;

    //region Try
    try {
      // deliverySettingCtrl.sink.add(DeliverySettingState.Loading);

      //If user is coming from add product and also carrying the return settings then update that setting
      if (isFromAddProduct && addProductLevelDeliverySettings.message != null) {
        productLevelDeliverySettings = SellerDeliveryStoreResponse(
            message: 'success',
            deliverySettingData: DeliverySettingData.copy(
                addProductLevelDeliverySettings.deliverySettingData!));
        //ReturnWarrantyData.copy(returnSettingsDataFromAddProduct!.data!)
        // productLevelDeliverySettings.deliverySettingData!.deliverysettingid = 0;
      }
      //If user is not coming from add product then get product level or return setting (From edit product or store level)
      else {
        //Api call to get data
        productLevelDeliverySettings =
            await sellerDeliverySettingsService.getDeliveryStoreSettings(
                storeRef: storeRef, productReference: productReference);
      }

      onChangeSetting();

      //If selected location is empty string

      //Previously selected Location
      // if(deliverySettingResponse.deliverySettingData!.deliveryLocations==""){
      //   selectedPinCodes.clear();
      // }
      // else{
      //   selectedPinCodes = deliverySettingResponse.deliverySettingData!.deliveryLocations!.split('|');
      //   deliveryLocations = deliverySettingResponse.deliverySettingData!.deliveryLocations!;
      //
      // }
      //Add flag that setting is available or not
      // isSettingAvailable
      productLevelDeliverySettings.deliverySettingData!.deliverysettingid == 0
          ? isSettingAvailable = false
          : isSettingAvailable = true;

      ///Get pin code
      //await getDeliveryPinCode();
      deliverySettingCtrl.add(DeliverySettingState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      deliverySettingCtrl.sink.add(DeliverySettingState.Failed);

      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      //print(error);
      deliverySettingCtrl.sink.add(DeliverySettingState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion

  //region Open tap save store default
  Future opTapSaveStoreDefault() {
    return CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          firstButtonName: AppStrings.yes,
          secondButtonName: AppStrings.no,
          onTapSave: (value) async {
            //Save as store default to true
            saveAsStoreDefault = true;
            //Refresh UI to show checkbox as selected
            deliverySettingCtrl.sink.add(DeliverySettingState.Success);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.applyingTheseSettingsAsTheDefault,
          popPreviousScreen: false,
        ));
  }

  //endregion

  ///Api Call (Add Data)
  //region Add Delivery Store Settings API Call
  void addDeliveryStoreSettings({
    required bool isDeliveryEnabled,
    required bool isPickupEnabled,
  }) async {
    try {
      // NEW SCENARIO: Reset to default when both toggles are off
      if (!isDeliveryEnabled && !isPickupEnabled) {
        productLevelDeliverySettings.deliverySettingData!.fulfillmentOptions =
            "";

        // Reset delivery-related fields to default values
        productLevelDeliverySettings.deliverySettingData!.timeToPrepare = 0;
        productLevelDeliverySettings.deliverySettingData!.timeToDeliver = 0;

        // Reset delivery method flags
        productLevelDeliverySettings.deliverySettingData!.deliverymethodSelf =
            false;
        productLevelDeliverySettings
            .deliverySettingData!.deliverymethodLogistics = false;
        productLevelDeliverySettings
            .deliverySettingData!.deliverymethodWhitelabel = false;

        // Clear delivery person details
        productLevelDeliverySettings.deliverySettingData!.deliveryPersonalName =
            "";
        productLevelDeliverySettings
            .deliverySettingData!.deliveryPersonalPhone = "";

        // Reset location-related fields
        productLevelDeliverySettings
            .deliverySettingData!.selectedLocationCount = 0;
        productLevelDeliverySettings.deliverySettingData!.deliveryLocations =
            "";

        // Reset pickup location details
        productLevelDeliverySettings.deliverySettingData!.pickupLocationIds =
            [];

        // Reset delivery fee details
        productLevelDeliverySettings
            .deliverySettingData!.deliveryfeetypeStandard = false;
        productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue = "";
        productLevelDeliverySettings
            .deliverySettingData!.deliveryfeetypeAllFree = false;

        // Reset logistics partner
        productLevelDeliverySettings
            .deliverySettingData!.defaultLogisticPartner = "";

        // Proceed with saving the reset configuration
        await sellerDeliverySettingsService.addDeliveryStoreSettings(
            storeReference: storeRef, productId: productId);

        CommonMethods.toastMessage(
            "Delivery settings reset to default", context);
        Navigator.pop(context);
        return;
      }

      // Existing toggle-based fulfillment options logic
      if (isDeliveryEnabled && isPickupEnabled) {
        productLevelDeliverySettings.deliverySettingData!.fulfillmentOptions =
            "DELIVERY_AND_IN_STORE_PICKUP";
      } else if (isDeliveryEnabled) {
        productLevelDeliverySettings.deliverySettingData!.fulfillmentOptions =
            "DELIVERY";
      } else if (isPickupEnabled) {
        productLevelDeliverySettings.deliverySettingData!.fulfillmentOptions =
            "IN_STORE_PICKUP";
      }

      // Update pickup location IDs from selected locations
      final selectedLocations = pickupLocations
          .where((loc) => loc.isPickupLocation ?? false)
          .toList();
      if (selectedLocations.isNotEmpty) {
        productLevelDeliverySettings.deliverySettingData!.pickupLocationIds =
            selectedLocations.map((loc) => loc.addressid.toString()).toList();
      } else {
        productLevelDeliverySettings.deliverySettingData!.pickupLocationIds =
            [];
      }

      // pickup locations must be empty if delivery is selected
      if (productLevelDeliverySettings
              .deliverySettingData!.fulfillmentOptions ==
          "DELIVERY") {
        productLevelDeliverySettings.deliverySettingData!.pickupLocationIds =
            [];
      }

      // Validate pickup locations if IN_STORE_PICKUP is enabled
      if ((productLevelDeliverySettings
                      .deliverySettingData?.fulfillmentOptions ==
                  "IN_STORE_PICKUP" ||
              productLevelDeliverySettings
                      .deliverySettingData?.fulfillmentOptions ==
                  "DELIVERY_AND_IN_STORE_PICKUP") &&
          (productLevelDeliverySettings
                  .deliverySettingData?.pickupLocationIds?.isEmpty ??
              true)) {
        CommonMethods.toastMessage(
            "Please add at least one pickup location", context);
        return;
      }

      // Delivery related validations - only run if delivery is enabled
      if (isDeliveryEnabled) {
        // Check time to prepare and delivery
        if (productLevelDeliverySettings.deliverySettingData!.timeToPrepare == null ||
            productLevelDeliverySettings.deliverySettingData!.timeToDeliver == null) {
          CommonMethods.toastMessage(
              AppStrings.pleaseSelectPrepareAndDelivery, context);
          return;
        }
        
        // Check time to delivery should be higher than time to prepare
        if (productLevelDeliverySettings.deliverySettingData!.timeToDeliver! <=
            productLevelDeliverySettings.deliverySettingData!.timeToPrepare!) {
          CommonMethods.toastMessage(
              AppStrings.estimatedDaysCanNotSmallerThenPrepareTime, context);
          return;
        }

        // If Self delivery is selected
        if (productLevelDeliverySettings.deliverySettingData!.deliverymethodSelf!) {
          // Check number and name are not null
          if (productLevelDeliverySettings.deliverySettingData!.deliveryPersonalPhone == null ||
              productLevelDeliverySettings.deliverySettingData!.deliveryPersonalPhone!.isEmpty ||
              productLevelDeliverySettings.deliverySettingData!.deliveryPersonalPhone!.length < 10 ||
              productLevelDeliverySettings.deliverySettingData!.deliveryPersonalName == null ||
              productLevelDeliverySettings.deliverySettingData!.deliveryPersonalName!.isEmpty) {
            CommonMethods.toastMessage(AppStrings.enterValidDelivery, context);
            return;
          }
        }

        // Check delivery locations if delivery is enabled
        if (productLevelDeliverySettings.deliverySettingData!.selectedLocationCount == 0) {
          CommonMethods.toastMessage(AppStrings.selectDeliveryLocation, context);
          return;
        }

        // If logistics is selected
        if (productLevelDeliverySettings.deliverySettingData!.deliverymethodLogistics! &&
            productLevelDeliverySettings.deliverySettingData!.defaultLogisticPartner == null) {
          CommonMethods.toastMessage(AppStrings.selectTheLogistic, context);
          return;
        }

        // Standard fee validation
        if (productLevelDeliverySettings.deliverySettingData!.deliveryfeetypeStandard == true) {
          if (productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue?.isEmpty == true ||
              productLevelDeliverySettings.deliverySettingData!.deliveryfeeValue == "") {
            CommonMethods.toastMessage(AppStrings.standardFeeCanNot, context);
            return;
          }
        }
      }

      ///If coming from store delivery setting (store level)
      if (productReference == null && !isFromAddProduct) {
        ///Add Api call for store level (productId: null)
        await sellerDeliverySettingsService.addDeliveryStoreSettings(
            storeReference: storeRef, productId: null);
      }

      ///If same delivery setting as store one then call delete delivery setting
      else if (storeLevelDeliverySettingResponse.deliverySettingData ==
              productLevelDeliverySettings.deliverySettingData &&
          storeLevelDeliverySettingResponse
                  .deliverySettingData!.deliverysettingid !=
              0) {
        // Reset to store level by deleting product-level setting
        sellerDeliverySettingsService.resetProductLevelSetting(
            storeReference: storeRef, productReference: productReference);
      }

      ///If coming from edit product and saveAsStoreDefault is true then delete the product level delivery setting and save the store level delivery setting
      else if (productReference != null && saveAsStoreDefault == true) {
        //1. Add store level setting
        await sellerDeliverySettingsService.addDeliveryStoreSettings(
            storeReference: storeRef, productId: null);
        //2. Delete product level setting
        sellerDeliverySettingsService.resetProductLevelSetting(
            storeReference: storeRef, productReference: productReference);
        if (context.mounted) {
          CommonMethods.toastMessage(
              AppStrings.yourDeliverySettingForThisProductIsSaved, context);
        }
      }

      ///If coming from add product
      else if (isFromAddProduct) {
        // If save as store default is checked, save to DB as store level
        if (saveAsStoreDefault) {
          await sellerDeliverySettingsService.addDeliveryStoreSettings(
              storeReference: storeRef, productId: null);
          if (context.mounted) {
            CommonMethods.toastMessage(
                AppStrings.yourDeliverySettingForThisStoreIsSaved, context);
          }
        }
        // If not save as store default, don't save to DB - will be saved during publish
        // Just return the settings to be saved later
      }

      ///If coming from edit product (save as product level immediately)
      else if (productReference != null && !isFromAddProduct) {
        ///Add Api call for product level
        await sellerDeliverySettingsService.addDeliveryStoreSettings(
            storeReference: storeRef, productId: productId);
      }

      ///Default case - save as product level if productId exists, otherwise store level
      else {
        ///Add Api call
        await sellerDeliverySettingsService.addDeliveryStoreSettings(
            storeReference: storeRef, productId: productId);
      }

      ///From edit product and saveAsStoreDefault is false
      if (productReference != null && isFromAddProduct == false) {
        if (context.mounted) {
          CommonMethods.toastMessage(
              AppStrings.yourDeliverySettingForThisProductIsSaved, context);
        }
      }

      ///If from store
      if (productReference == null && !isFromAddProduct) {
        if (context.mounted) {
          CommonMethods.toastMessage(
              AppStrings.yourDeliverySettingForThisStoreIsSaved, context);
        }
      }

      if (context.mounted) {
        // If coming from add product, return the settings data
        if (isFromAddProduct) {
          // Create response with current settings and saveAsStoreDefault flag
          var responseToReturn = SellerDeliveryStoreResponse(
            message: 'success',
            deliverySettingData: DeliverySettingData.copy(
                productLevelDeliverySettings.deliverySettingData!),
            saveAsStoreDefault: saveAsStoreDefault,
          );
          Navigator.pop(context, responseToReturn);
        } else {
          Navigator.pop(context);
        }
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion

  //region Get logistics partners
  void getLogisticsPartner() async {
    //region Try
    try {
      //Loading
      // deliverySettingCtrl.sink.add(DeliverySettingState.Loading);
      logisticsPartnerResponse =
          await logisticsPartnerServices.getLogisticsPartners();
      //Success
      // deliverySettingCtrl.sink.add(DeliverySettingState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      deliverySettingCtrl.sink.add(DeliverySettingState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //getDeliveryPinCode();
      //print(error);
      deliverySettingCtrl.sink.add(DeliverySettingState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion

  //region On tap logistics partner
  void onTapLogisticsPartners() {
    List<String> dataList = [];
    for (var data in logisticsPartnerResponse.data!) {
      dataList.add(data.courierName!);
    }

    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectLogisticPartner,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      //Add value
      productLevelDeliverySettings.deliverySettingData!.defaultLogisticPartner =
          value;
      //On change settings
      onChangeSetting();
      //Success
      deliverySettingCtrl.sink
          .add(DeliverySettingState.Success); // selectedCategory = value;
      // giveFeedbackCtrl.sink.add(GivefeedbackState.Success);
    });
  }

  //endregion

  //region On tap reset
  Future<void> onTapReset() async {
    //region Try
    try {
      deliverySettingCtrl.sink.add(DeliverySettingState.Loading);
      productLevelDeliverySettings = await sellerDeliverySettingsService
          .getDeliveryStoreSettings(storeRef: storeRef, productReference: null);

      onChangeSetting();
      //If selected location is empty string

      //Previously selected Location
      // if(deliverySettingResponse.deliverySettingData!.deliveryLocations==""){
      //   selectedPinCodes.clear();
      // }
      // else{
      //   selectedPinCodes = deliverySettingResponse.deliverySettingData!.deliveryLocations!.split('|');
      //   deliveryLocations = deliverySettingResponse.deliverySettingData!.deliveryLocations!;
      //
      // }
      //Add flag that setting is available or not
      // isSettingAvailable
      productLevelDeliverySettings.deliverySettingData!.deliverysettingid == 0
          ? isSettingAvailable = false
          : isSettingAvailable = true;

      ///Get pin code
      //await getDeliveryPinCode();
      deliverySettingCtrl.add(DeliverySettingState.Success);
      _resetPickupLocationsToDefault();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      //getDeliveryPinCode();
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  void _resetPickupLocationsToDefault() {
    // Reset all pickup locations to match store-level settings
    if (storeLevelDeliverySettingResponse.deliverySettingData != null) {
      // If store-level settings exist, reset product-level pickup locations
      for (var location in pickupLocations) {
        location.isPickupLocation = false;
      }

      // If store-level has pickup locations, apply those
      if (storeLevelDeliverySettingResponse
              .deliverySettingData!.pickupLocationIds !=
          null) {
        final storePickupLocationIds = storeLevelDeliverySettingResponse
            .deliverySettingData!.pickupLocationIds!;

        for (var location in pickupLocations) {
          location.isPickupLocation =
              storePickupLocationIds.contains(location.addressid.toString());
        }
      }

      // Update the notifier to trigger UI refresh
      pickupLocationsNotifier.value = List.from(pickupLocations);
    }
  }

  //endregion

  ///On tap select location
  //region On tap select location

  void onTapSelectLocation() async {
    if (sellerDeliveryLocationPinCodeResponse.message == null) {
      openPinCodeList();
      await getDeliveryPinCode();
    } else {
      openPinCodeList();
      pinCodeStateCtrl.sink.add(PinCodeState.Success);
    }
  }

  //endregion

  //region Get Delivery PinCode
  Future<void> getDeliveryPinCode() async {
    //region Try
    try {
      //Loading
      pinCodeStateCtrl.sink.add(PinCodeState.Loading);
      //Api call
      sellerDeliveryLocationPinCodeResponse =
          await sellerDeliverySettingsService.getDeliveryPinCode(
              isSettingAvailable ? storeRef : "0",
              isSettingAvailable ? productId : 0);

      // If gst exist is false then remove all except the state
      if (!storeLevelDeliverySettingResponse
          .deliverySettingData!.isGstExists!) {
        sellerDeliveryLocationPinCodeResponse.data!.first.states!
            .removeWhere((element) {
          return element.state!.toLowerCase() !=
              storeLevelDeliverySettingResponse
                  .deliverySettingData!.trustCenterLocationState!
                  .toLowerCase();
        });
      }

      //print(sellerDeliveryLocationPinCodeResponse.data!.first.states!.length);

      allStateSelected =
          sellerDeliveryLocationPinCodeResponse.data!.first.allBarath!;
      //print(sellerDeliveryLocationPinCodeResponse.data);
      //
      // //print("Store state is ${StoreDashBoardBloc.singleStoreInfoResponse.data!.storeDetails!.first.state }");
      // //Move store created state to top
      // if(StoreDashBoardBloc.singleStoreInfoResponse.data!.storeDetails!.first.state !=null){
      //   var sameState = sellerDeliveryLocationPinCodeResponse.data!.first.states!.firstWhere((element) => element.state==StoreDashBoardBloc.singleStoreInfoResponse.data!.storeDetails!.first.state !);
      //   sellerDeliveryLocationPinCodeResponse.data!.first.states!.insert(0,sameState);
      // }
      // //Remove duplicates
      // sellerDeliveryLocationPinCodeResponse.data!.first.states = sellerDeliveryLocationPinCodeResponse.data!.first.states!.toSet().toList();
      //
      //Success
      pinCodeStateCtrl.sink.add(PinCodeState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      //print(error);
      // var snackBar = SnackBar(content: Text(error.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return;
    }
  }

  //endregion

  ///Open location dialog
  //region Open Pin Code List
  void openPinCodeList() {
    // if(selectedPinCodes.isEmpty) {
    //   return;
    // }
    showDialog(
        context: context,
        builder: (BuildContext pinCodeContext) {
          return Material(
            child: StreamBuilder<PinCodeState>(
                initialData: PinCodeState.Loading,
                stream: pinCodeStateCtrl.stream,
                builder: (context, snapshot) {
                  if (snapshot.data == PinCodeState.Loading &&
                      sellerDeliveryLocationPinCodeResponse.message == null) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        AppCommonWidgets.appCircularProgress(),
                        verticalSizedBox(20),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            AppStrings
                                .pleaseWaitWhileWeAreFetchingDeliveryLocation,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                        )
                      ],
                    );
                  }
                  // //Fill location data
                  // MarkSelectedLocation(this).fillSelectedDataIntoLocationTree();
                  return DeliveryServiceLocationScreen(
                    sellerStoreDeliverySettingBloc: this,
                  );
                }),
          );
        }).then((value) {
      //Refresh ui
      deliverySettingCtrl.sink.add(DeliverySettingState.Success);
    });
  }

  //endregion

  //region On tap save
  void onTapSave() {
    addDeliveryStoreSettings(
      isDeliveryEnabled: productLevelDeliverySettings
              .deliverySettingData!.deliverymethodWhitelabel ??
          false,
      isPickupEnabled:
          pickupLocations.any((loc) => loc.isPickupLocation ?? false),
    );
  }

  //endregion

  //region Go to Trust Center
  void goToTrustCenter() {
    var screen = SellerTrustCenterScreen(
      storeRef: AppConstants.appData.storeReference!,
      isStoreOwnerView: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      init();
    });
  }

  //endregion

  //region Is trust center available check
  bool isTrustCenterActive() {
    bool trustCenterAvailableStatus = false;
    // Check if any one id info is present
    if (storeLevelDeliverySettingResponse.deliverySettingData!.isPanExists! ||
        storeLevelDeliverySettingResponse.deliverySettingData!.isGstExists!) {
      //Check state info
      if (storeLevelDeliverySettingResponse
          .deliverySettingData!.trustCenterLocationState!.isEmpty) {
        trustCenterAvailableStatus = false;
      } else {
        trustCenterAvailableStatus = true;
      }
    }
    //Else return false
    else {
      return trustCenterAvailableStatus;
    }
    // deliverySettingCtrl.sink.add(DeliverySettingState.Success);

    return trustCenterAvailableStatus;
  }

  //endregion

  //region Get current delivery settings
  DeliverySettingData? getCurrentDeliverySettings() {
    // Prioritize product-level settings
    if (productLevelDeliverySettings.deliverySettingData != null &&
        (productLevelDeliverySettings.deliverySettingData!.deliverysettingid ??
                0) !=
            0) {
      return productLevelDeliverySettings.deliverySettingData;
    }

    // Fallback to store-level settings
    if (storeLevelDeliverySettingResponse.deliverySettingData != null &&
        (storeLevelDeliverySettingResponse
                    .deliverySettingData!.deliverysettingid ??
                0) !=
            0) {
      return storeLevelDeliverySettingResponse.deliverySettingData;
    }

    // Return null if no settings are available
    return null;
  }
  //endregion

  //region Dispose
  void dispose() {
    isSameSettingCtrl.close();
    deliverySettingCtrl.close();
    pickupLocationsNotifier.dispose();
  }
  //endregion
}
