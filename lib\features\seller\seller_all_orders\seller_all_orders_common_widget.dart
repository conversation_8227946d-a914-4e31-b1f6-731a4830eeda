import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/cancelled_or_returned_products_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/variant_display_widget/variant_display_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/all_order_filter/all_order_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

String _getRefundText(SubOrder subOrder) {
  final refundDetails = subOrder.refundDetails;
  if (refundDetails != null && refundDetails.isNotEmpty) {
    final amount = refundDetails.first.refundedAmount;
    if (amount != null) {
      return "You will receive ₹$amount as the refund amount";
    }
  }
  return "You will receive ₹0 as the refund amount";
}

//region Product Detail
Widget productInfoCard({
  required BuildContext context,
  required SubOrder subOrder,
  bool isConfirmedVisible = false,
  bool isPaidOnVisible = false,
  bool isDeliveryReturnOnVisible = false,
  bool isReturnWindowLastDateVisible = false,
  bool isRequestOnTrackPackageVisible = false,
  bool isOnlyReturnOnVisible = false,
  bool isCancelledOnVisible = false,
  bool isYouWillReceiveVisible = false,
  Widget customWidget = const SizedBox(),
}) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      //Custom widget
      customWidget,
      //Confirmed date
      Visibility(
        visible: isConfirmedVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: Text(
            "Confirmed on\n${CommonMethods.convertStringDateTimeSlashFormat(subOrder.confirmationDate ?? "02/05/2023 19:20:08")}",
            maxLines: 2,
            overflow: TextOverflow.visible,
            textAlign: TextAlign.left,
            style: const TextStyle(
              fontFamily: "LatoSemiBold",
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack,
            ),
          ),
        ),
      ),
      //Paid on
      Visibility(
        visible: isPaidOnVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: const Text(
            "Paid on\n24-01-2021",
            maxLines: 2,
            overflow: TextOverflow.visible,
            textAlign: TextAlign.left,
            style: TextStyle(
              fontFamily: "LatoSemiBold",
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack,
            ),
          ),
        ),
      ),
      //Delivery Successfully at and Return requested on
      Visibility(
        visible: isDeliveryReturnOnVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: const Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Delivered successfully at \n24-01-2021, 11:25 AM",
                maxLines: 2,
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
              Text(
                "Return requested on\n24-01-2021",
                maxLines: 2,
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
            ],
          ),
        ),
      ),
      //Return window last date
      Visibility(
        visible: isReturnWindowLastDateVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                "Return window last date: 14-02-2021",
                maxLines: 2,
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
              SvgPicture.asset(
                AppImages.exclamation,
                color: AppColors.darkGray,
              )
            ],
          ),
        ),
      ),
      //Return request on and track package
      Visibility(
        visible: isRequestOnTrackPackageVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                "Return requested on\n24-01-2021",
                maxLines: 2,
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontFamily: "LatoSemiBold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                decoration: const BoxDecoration(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                ),
                child: const Center(
                  child: Text(
                    "Track the package",
                    style: TextStyle(
                        fontFamily: "LatoBold",
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                        color: AppColors.appWhite),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      //Only return
      Visibility(
        visible: isOnlyReturnOnVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: Text(
            "Returned on ${subOrder.returnedDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(subOrder.returnedDate!)}",
            maxLines: 2,
            overflow: TextOverflow.visible,
            textAlign: TextAlign.left,
            style: const TextStyle(
              fontFamily: "LatoSemiBold",
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.appBlack,
            ),
          ),
        ),
      ),
      //Cancelled date
      Visibility(
        visible: isCancelledOnVisible,
        child: Container(
          margin: const EdgeInsets.only(bottom: 10),
          child: Text(
              "Cancelled on: ${subOrder.cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(subOrder.cancelledDate!)}",
              maxLines: 2,
              overflow: TextOverflow.visible,
              textAlign: TextAlign.left,
              style: AppTextStyle.contentHeading0(
                textColor: AppColors.appBlack,
              )),
        ),
      ),

      //You will receive
      Visibility(
        visible: isYouWillReceiveVisible,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                _getRefundText(subOrder),
                maxLines: 2,
                overflow: TextOverflow.visible,
                textAlign: TextAlign.left,
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
            SvgPicture.asset(
              AppImages.exclamation,
              height: 20,
              color: AppColors.darkGray,
            )
          ],
        ),
      ),

      InkWell(
        onTap: () {
          var screen = BuyerViewSingleProductScreen(
            productVersion: subOrder.productVersion,
            productReference: subOrder.productReference,
            productLatestVersion: subOrder.productLatestVersion,
            subOrderNumber: subOrder.suborderNumber,
            isGoToLatestVisible:
                subOrder.productVersion == subOrder.productLatestVersion
                    ? false
                    : true,
          );
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);
        },
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          // height: 60,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //Product name
                    ///Brand and product name
                    RichText(
                      textScaleFactor: MediaQuery.textScaleFactorOf(
                          AppConstants.globalNavigator.currentContext!),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: subOrder.productBrand,
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                          TextSpan(
                              text: " ${subOrder.productName}",
                              // text:"Nykaa Matte Nail Lacquer & Nail Enamel 2 in 1 coloNykaa Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...rs with",
                              style: AppTextStyle.contentText0(
                                textColor: AppColors.appBlack,
                              )),
                        ],
                      ),
                    ),
                    verticalSizedBox(5),

                    // Variant information display
                    OrderVariantDisplayWidget(
                      variantDetails: subOrder.variantDetails,
                      showPriceInfo: false,
                    ),

                    if (subOrder.variantDetails != null) verticalSizedBox(5),

                    //Price
                    Text(
                      "₹${subOrder.sellingPrice} X ${subOrder.productQuantity} = ₹${subOrder.sellingPrice! * subOrder.productQuantity!}"
                      "${subOrder.suborderFeeDetails!.productLevelDeliveryFee! == 0 ? "" : "    Delivery: ₹${subOrder.suborderFeeDetails!.productLevelDeliveryFee!}"}",
                      style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack,
                      ),
                    )
                  ],
                ),
              ),
              horizontalSizedBox(20),
              //Product image
              ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  child: Container(
                      color: AppColors.textFieldFill1,
                      height: 60,
                      width: 60,
                      child: extendedImage(
                        "${subOrder.productImage}",
                        customPlaceHolder: AppImages.productPlaceHolder,
                        context,
                        100,
                        100,
                        cache: true,
                      )))
            ],
          ),
        ),
      ),
    ],
  );
}

//endregion

//region Tip
Widget tip(String message) {
  return Container(
    color: AppColors.appWhite,
    child: Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Container(
          decoration: BoxDecoration(
              //color: Colors.red,
              border: Border.all(width: 1.0, color: AppColors.lightStroke),
              borderRadius: const BorderRadius.all(Radius.circular(30))),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                AppImages.yellowLightBulb,
                fit: BoxFit.cover,
              ),
              horizontalSizedBox(5),
              Expanded(
                child: Text(
                  message,
                  maxLines: 1,
                  style: const TextStyle(
                      fontSize: 12,
                      overflow: TextOverflow.visible,
                      fontFamily: "LatoRegular",
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack),
                ),
              ),
              horizontalSizedBox(5),
              SvgPicture.asset(
                AppImages.close,
                fit: BoxFit.cover,
              ),

              // Expanded(child: horizontalSizedBox(5)),
            ],
          ),
        ),
      ),
    ),
  );
}
//endregion

//region Blue button
Widget sellerAllOrderActionButton(
    {onPress,
    String buttonName = "",
    Color colors = AppColors.inActiveGreen,
    Color textColor = AppColors.brandBlack}) {
  return Expanded(
    child: InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        decoration: BoxDecoration(
          color: colors,
          borderRadius: const BorderRadius.all(Radius.circular(100)),
        ),
        child: Center(
          child: Text(buttonName,
              style: AppTextStyle.button2Bold(textColor: AppColors.appWhite)),
        ),
      ),
    ),
  );
}
//endregion

//region Grey button
Widget sellerAllOrderCancelButton({onPress, String buttonName = ""}) {
  return Expanded(
    child: InkWell(
      onTap: () {
        onPress();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 20),
        decoration: const BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: Center(
          child: Text(buttonName,
              style: AppTextStyle.button2Bold(textColor: AppColors.appBlack)),
        ),
      ),
    ),
  );
}
//endregion

//region Bottom sheet Sub orders list view
Widget bottomSheetViewSubOrderList(
    {required List<SubOrder> suborderList,
    required BuildContext context,
    String? subOrderStatus,
    bool isCheckBoxVisible = false}) {
  return Container(
    margin: const EdgeInsets.all(5),
    decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.lightGray2,
          width: 1,
        ),
        borderRadius: const BorderRadius.all(Radius.circular(10))),
    child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        // itemCount: suborderList.length,
        itemCount: suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppCommonWidgets.subOrderInfo(
                    subOrder: suborderList[index],
                    onTap: () {},
                    context: context,
                    isCheckBoxVisible: false,
                  ),
                ],
              ),
              index == suborderList.length - 1 ? const SizedBox() : divider()
            ],
          );
        }),
  );
}
//endregion

//region Sub Order Tag
Widget subOrderTag(String suborderTag) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    decoration: const BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.all(Radius.circular(30))),
    child: appText(suborderTag,
        color: AppColors.appBlack,
        fontWeight: FontWeight.w400,
        fontFamily: "LatoRegular",
        fontSize: 12,
        maxLine: 1),
  );
}
//endregion

class SellerAllOrdersCommonWidgets {
  //region  Search Filled
  static Widget searchField({
    required BuildContext context,
    required TextEditingController textFieldCtrl,
    required AllOrderFilterBloc allOrderFilterBloc,
  }) {
    return StreamBuilder<bool>(
        stream: null,
        builder: (context, snapshot) {
          return Row(
            children: [
              Expanded(
                child: TextField(
                  controller: textFieldCtrl,
                  autofillHints: const [AutofillHints.email],
                  textCapitalization: TextCapitalization.words,

                  ///On change field
                  onChanged: (value) {
                    allOrderFilterBloc.onSearch(value: value);
                  },

                  ///Style
                  style: const TextStyle(
                      fontFamily: AppConstants.rRegular,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      height: 1.19,
                      color: AppColors.appBlack),

                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.all(0),
                    filled: true,
                    fillColor: AppColors.textFieldFill1,

                    hintText: AppStrings.hintTextSearchVith,

                    ///Hint text style
                    hintStyle: TextStyle(
                      height: 1.19,
                      fontSize: 14,
                      // letterSpacing: hintTextLetterSpacing,
                      fontFamily: AppConstants.rRegular,
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack.withOpacity(0.4),
                    ),

                    ///Suffix icon
                    // suffix: Icon(Icons.close),

                    suffixIcon: Visibility(
                      visible: false,
                      child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {},
                        child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 15, horizontal: 15),
                            child: SvgPicture.asset(AppImages.searchBarIcon)),
                      ),
                    ),

                    ///Prefix icon
                    prefixIcon: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 15),
                        child: SvgPicture.asset(AppImages.searchBarIcon)),

                    ///Focus border
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(22),
                      borderSide: const BorderSide(
                          color: AppColors.textFieldFill1, width: 1.5),
                    ),

                    ///Enable border
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(22),
                      borderSide: const BorderSide(
                          color: AppColors.textFieldFill1, width: 1.5),
                    ),
                  ),
                ),
              ),
              CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {},
                  child: SvgPicture.asset(AppImages.filter))
            ],
          );
        });
  }
//endregion

//region Component card
  static Widget sellerCommonComponent({
    required String icon,
    required String componentName,
    required List<SubOrder> suborderList,
    required Widget additionalWidgets,
    bool isSellerSideDelivered = false,
    bool isEstimateDeliveryShow = false,
    bool isBuyerSideDeliveredOnShow = false,
    bool isSellerSidePickupDateShow = false,
    bool isBuyerSidePickupDateShow = false,
    bool isSellerSideEstimatedReturnPickupDateShow = false,
    bool isSellerSideEstimatedReturnDeliveryDateShow = false,
    bool isSellerSideReturned = false,
    bool isSellerSideReturnedDateShow = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Icon and status
        Container(
          alignment: Alignment.centerLeft,
          // height: 25,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              icon.endsWith("svg")
                  ? SvgPicture.asset(
                      icon,
                      height: 25,
                    )
                  : Image.asset(
                      icon,
                      height: 25,
                    ),
              horizontalSizedBox(10),
              Expanded(
                  child: Text(
                componentName,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access1(textColor: AppColors.appBlack),
              )),
            ],
          ),
        ),
        verticalSizedBox(15),
        //Sub order,items
        Container(
          // height: 20,
          alignment: Alignment.centerLeft,
          child: Text(
            "# ${CommonMethods.calculateSubOrdersInSuborderList(subOrderList: suborderList)}",
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          ),
        ),
        verticalSizedBox(5),

        //Delivered
        isEstimateDeliveryShow && isSellerSideDelivered
            ? Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(
                  "${AppStrings.deliveredSuccessfully} ${CommonMethods.dateTimeAmPm(date: suborderList.first.deliveredDate!)[1]}",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
              )
            : const SizedBox(),

        //Scheduled for pickup seller
        isSellerSidePickupDateShow &&
                !isSellerSideDelivered &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.estimatedPickupDate} ${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.estimatedPickupDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Scheduled for pickup buyer
        isBuyerSidePickupDateShow &&
                !isSellerSideDelivered &&
                AppConstants.appData.isUserView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.estimatedDeliveryDate}: ${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.estimatedDeliveryDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Seller side Estimate return pickup date
        isSellerSideEstimatedReturnPickupDateShow &&
                !isSellerSideReturned &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.estimatedReturnPickupDate}${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.returnEstimatedPickupDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Seller side returned date
        isSellerSideReturnedDateShow &&
                isSellerSideReturned &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.returnedOn}${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.returnedDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Seller side returned date
        isSellerSideReturnedDateShow &&
                isSellerSideReturned &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                    child: RichText(
                      textScaleFactor: MediaQuery.textScaleFactorOf(
                          AppConstants.globalNavigator.currentContext!),

                      // overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text:
                                "Refunds will be auto released in 24 hours unless you request to ",
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                          TextSpan(
                              text: "Hold Refund",
                              // text:"Nykaa Matte Nail Lacquer & Nail Enamel 2 in 1 coloNykaa Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...rs with",
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack,
                                  isUnderline: true)),
                          TextSpan(
                              text:
                                  " due to an issue with returned product. Speak with your customer to avoid any issues here.",
                              // text:"Nykaa Matte Nail Lacquer & Nail Enamel 2 in 1 coloNykaa Matte Nail Lacquer & Nail Enamel 2 in 1 colors with...rs with",
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack,
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),

        //Seller side Estimate return delivery date
        isSellerSideEstimatedReturnDeliveryDateShow &&
                !isSellerSideReturned &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.estimatedReturnDeliveryDate}${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.returnEstimatedDeliveryDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Buyer side Estimate delivery
        isEstimateDeliveryShow &&
                !isSellerSideDelivered &&
                AppConstants.appData.isUserView!
            ? Row(
                children: [
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      "${AppStrings.closestEstimateDeliveryDate} ${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.estimatedDeliveryDate!.replaceAll("-", "/")).toList())}",
                      maxLines: 2,
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    ),
                  )),
                ],
              )
            : const SizedBox(),

        //Seller side Estimate delivery
        isEstimateDeliveryShow &&
                !isSellerSideDelivered &&
                AppConstants.appData.isStoreView!
            ? Row(
                children: [
                  Expanded(
                    child: CommonMethods.isValidDeliveryDate(
                      givenDate: CommonMethods.minimumAndMaximumDateTime(
                          dateList: suborderList
                              .map((e) =>
                                  e.estimatedDeliveryDate!.replaceAll("-", "/"))
                              .toList(),
                          isMinimum: true),
                    )

                        //If valid delivery date
                        ? Container(
                            margin: const EdgeInsets.only(bottom: 10),
                            child: Text(
                              "${AppStrings.closestEstimateDeliveryDate} ${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.estimatedDeliveryDate!.replaceAll("-", "/")).toList())}",
                              maxLines: 2,
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack),
                            ),
                          )
                        :
                        //If not a valid delivery date
                        AppToolTip(
                            message: AppStrings.updateDeliverDate,
                            toolTipWidget: Container(
                              margin: const EdgeInsets.only(bottom: 10),
                              child: Text(
                                "${AppStrings.closestEstimateDeliveryDate} ${CommonMethods.minimumAndMaximumDateTime(dateList: suborderList.map((e) => e.estimatedDeliveryDate!.replaceAll("-", "/")).toList())}",
                                maxLines: 2,
                                style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.red),
                              ),
                            ),
                          ),
                  ),
                ],
              )
            : const SizedBox(),
        //Buyer side delivered on
        Visibility(
          visible: isBuyerSideDeliveredOnShow,
          child: suborderList.first.deliveredDate == null
              ? const SizedBox()
              : Container(
                  // height: 20,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "${AppStrings.deliveredOn} ${CommonMethods.dateTimeAmPm(date: suborderList.first.deliveredDate!)[1]} ${CommonMethods.dateTimeAmPm(date: suborderList.first.deliveredDate!)[2]}",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.brandBlack),
                  ),
                ),
        ),
        additionalWidgets,
        verticalSizedBox(10),
      ],
    );
  }
//endregion

  //region How refund amount is calculated?
  static Widget howRefundAmountCalculated(
      {required List<SubOrder> subOrderList,
      required SellerSubOrderBloc sellerSubOrderBloc}) {
    return InkWell(
      onTap: () {
        var screen = CancelledOrReturnedProductsScreen(
          subOrderList: subOrderList,
          order: sellerSubOrderBloc.getSubOrdersResponse.orderList!.first,
          isSellerView: true,
        );
        var route = MaterialPageRoute(builder: (context) => screen);
        Navigator.push(sellerSubOrderBloc.context, route);
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 13),
        decoration: const BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: Text(
          AppStrings.howRefundAmountIsCalculated,
          style: AppTextStyle.button2Bold(textColor: AppColors.appWhite),
        ),
      ),
    );
  }
//endregion

//region Seller component bottom sheet sub-title
  static Widget sellerBottomSheetSubTitle({required String title}) {
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: AppTextStyle.subTitle(textColor: AppColors.writingBlack1),
        )),
      ],
    );
  }
//endregion
}
