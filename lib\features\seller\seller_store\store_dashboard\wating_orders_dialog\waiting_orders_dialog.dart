import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/app.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/wating_orders_dialog/waiting_orders_dialog_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class WaitingOrdersDialog extends StatefulWidget {
  const WaitingOrdersDialog({super.key});

  @override
  State<WaitingOrdersDialog> createState() => _WaitingOrdersDialogState();
}

class _WaitingOrdersDialogState extends State<WaitingOrdersDialog> {
  //region Bloc
  late WaitingOrdersDialogBloc waitingOrdersDialogBloc;
  //endregion

  //region Init
  @override
  void initState() {
    waitingOrdersDialogBloc = WaitingOrdersDialogBloc(context);
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          waitingOrderMessage(),
          const SizedBox(
            height: 15,
          ),
          waitingOrderButton(),
        ],
      ),
    );
  }
//endregion

//region Waiting order messaeg
  Widget waitingOrderMessage() {
    return Consumer<StoreDashboardDataModel>(
      builder:
          (BuildContext context, StoreDashboardDataModel value, Widget? child) {
        return Text(
          "${value.storeDashBoard.orderAlertMessage}",
          style: AppTextStyle.access0(
            textColor: AppColors.appBlack,
          ),
        );
      },
    );
  }
//endregion

//region Waiting order button
  Widget waitingOrderButton() {
    return Consumer<StoreDashboardDataModel>(
      builder:
          (BuildContext context, StoreDashboardDataModel value, Widget? child) {
        return Container(
          width: double.infinity,
          child: CupertinoButton(
              borderRadius: BorderRadius.circular(120),
              padding: const EdgeInsets.symmetric(vertical: 7, horizontal: 15),
              color: AppColors.brandBlack,
              child: Text(
                "View ${CommonMethods.singularPluralOnlyString(item: value.storeDashBoard.waitingForConfirmation!, singular: "order", plural: "orders")}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access1(textColor: AppColors.appWhite),
              ),
              onPressed: () async {
                Navigator.of(context).pop();
                waitingOrdersDialogBloc.goToSellerAllOrder();
              }),
        );
      },
    );
  }
//endregion
}
