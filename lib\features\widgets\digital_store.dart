import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class DigitalStoreWidget extends StatelessWidget {
  final bool isVisible;
  final String message;

  const DigitalStoreWidget({Key? key, required this.isVisible, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Screen width calculation without using MediaQuery height
    final double screenWidth = MediaQuery.of(context).size.width;
    final double borderWidth = screenWidth * 0.02; // Adjust as needed

    return Row(
      children: [
        Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.03),
            border: Border.all(
              color: AppColors.brandBlack,
              width: 1.3,
            ),
          ),
          child: isVisible
              ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // const Icon(Icons.public, color: Colors.black,size: 20,),
              SvgPicture.asset(AppImages.PhygitalStore,height: 22,),
              const SizedBox(width: 10),
              Text(
                  message,
                style: AppTextStyle.smallText(textColor: AppColors.appBlack).copyWith(height: 0)
              ),
            ],
          )
              : SizedBox.shrink(), // Invisible when isVisible is false
        ),
      ],
    );
  }
}
