import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_product_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum StoreProductsPaginationState { Loading, Done }

class StoreProductPagination {
  //region Context
  late BuildContext context;
  late StoreProductBloc storeProductBloc;
  // bool isLoadingPaginationData = false;
  StoreProductsPaginationState currentApiCallStatus = StoreProductsPaginationState.Done;

  //endregion

  //region Controller
  final storeProductPaginationCtrl = StreamController<StoreProductsPaginationState>.broadcast();
  //endregion

//region Constructor
  StoreProductPagination(this.context, this.storeProductBloc);
//endregion

  //region On pagination loading visible
  void onPaginationLoadingVisible() async {
    await getPaginationFeeds();
  }

  //endregion

  //region Get pagination recommended products
  Future<void> getPaginationFeeds() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      //If api call status is Loading then return
      if(currentApiCallStatus == StoreProductsPaginationState.Loading){
        print("Pagination already loading, skipping...");
        return;
      }

      print("Starting pagination load. Current products: ${storeProductBloc.storeProductList.length}");

      //Loading
      storeProductPaginationCtrl.sink.add(StoreProductsPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = StoreProductsPaginationState.Loading;
      //Api call
      // List<Product> productList = await RecommendedStoreAndUserServices().getRecommendedProducts(limit: 5, offset: storeProductBloc.storeProductList.length);
      var data  = await StoreProductServices().getBuyerStoreProduct(storeReference: storeProductBloc.storeReference, limit: 5, offset:storeProductBloc.storeProductList.length );

      print("Pagination API response: ${data.data?.length ?? 0} products");

      //Add in search recommended bloc recommendedProductList
      storeProductBloc.storeProductList.addAll(data.data!);
      //Add recommended store to data model
      productDataModel.addProductIntoList(products: data.data!);

      print("Total products after pagination: ${storeProductBloc.storeProductList.length}");

      //If feed list is empty
      if (data.data!.isEmpty) {
        print("No more products to load");
        //Current api call status is Done
        currentApiCallStatus = StoreProductsPaginationState.Done;
        //Empty
        return storeProductPaginationCtrl.sink.add(StoreProductsPaginationState.Done);
      }

      // Trigger UI rebuild to show new products
      storeProductBloc.storeProductsCtrl.sink.add(StoreProductListState.Success);

      //Current api call status is Done
      currentApiCallStatus = StoreProductsPaginationState.Done;
      //Done
      storeProductPaginationCtrl.sink.add(StoreProductsPaginationState.Done);

    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
      //Done
      storeProductPaginationCtrl.sink.add(StoreProductsPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = StoreProductsPaginationState.Done;

    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
      //Done
      storeProductPaginationCtrl.sink.add(StoreProductsPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = StoreProductsPaginationState.Done;
    }
  }
//endregion
}