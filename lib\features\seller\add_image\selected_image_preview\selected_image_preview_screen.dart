import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_image/selected_image_preview/selected_image_preview_bloc.dart';
import 'package:swadesic/model/product_images.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:reorderables/reorderables.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../util/app_constants.dart';



// region Selected Image PreviewScreen
class SelectedImagePreviewScreen extends StatefulWidget {
  //final ProductImageResponse? productImageResponse;
  final int? productId;

  const SelectedImagePreviewScreen({Key? key, this.productId}) : super(key: key);

  @override
  _SelectedImagePreviewScreenState createState() => _SelectedImagePreviewScreenState();
}
// endregion

class _SelectedImagePreviewScreenState extends State<SelectedImagePreviewScreen> {
  // region Bloc
  late SelectedImagePreviewBloc selectedImagePreviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    selectedImagePreviewBloc = SelectedImagePreviewBloc(context, widget.productId);
    selectedImagePreviewBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,

      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.productImage,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: true,

        textButtonWidget: SvgPicture.asset(AppImages.done),
        onTapTextButton: (){
          Navigator.pop(context);        }

    );
  }

  //endregion

  //region AppBar
  // AppBar appBar(){
  //   return AppBar(
  //     backgroundColor: AppColors.white,
  //     leading: CupertinoButton(
  //         onPressed: (){
  //           Navigator.pop(context);
  //         },
  //         padding: EdgeInsets.zero,
  //         child: SvgPicture.asset(AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill)),
  //     elevation: 0,
  //     centerTitle: false,
  //     titleSpacing: 0,
  //     title: Text(
  //       AppStrings.productImage,
  //       style: TextStyle(
  //         fontSize: 19,
  //         fontWeight: FontWeight.w700,
  //         color: AppColors.appBlack,
  //       ),
  //     ),
  //     automaticallyImplyLeading: false,
  //     //region Done Button
  //     actions: [
  //       CupertinoButton(
  //           child: SvgPicture.asset(AppImages.done,color: AppColors.appBlack,fit: BoxFit.fill,),
  //           onPressed: (){
  //             // widget.addProductToSelectedImage!.isEmpty?
  //             // selectedImagePreviewBloc.goBackToAddProduct():
  //             Navigator.pop(context);
  //             // selectedImagePreviewBloc.goBackToAddProduct();
  //
  //           }),
  //       CupertinoButton(
  //           onPressed: (){},
  //           child: SvgPicture.asset(AppImages.drawerIcon,color: AppColors.appBlack,height: 24,)),
  //
  //
  //     ],
  //
  //     //endregion
  //   );
  // }

  //endregion






  // region Body
  Widget body() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        verticalSizedBox(10),
        SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.width,

            child: bigImage()),
        //previouslyAddedImagesText(),
        verticalSizedBox(10),
        //previousImageList(),
        dragReorderText(),
        verticalSizedBox(10),
         Expanded(child: imageList()),
        verticalSizedBox(10),
      ],
    );
  }

  // endregion



  //region Big image
    Widget bigImage() {
      if(AppConstants.multipleSelectedImage.isEmpty){
        return SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.width,
        );
      }
      return StreamBuilder<ProductImages>(
          stream: selectedImagePreviewBloc.selectedImageCtrl.stream,
          builder: (context, snapshot) {

            if(AppConstants.multipleSelectedImage.isEmpty){
              return SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.width,

                child: Center(
                  child: Image.file(
                    File(snapshot.data!.image.path),
                    fit: BoxFit.cover,
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.width,


                  ),
                ),
              );
            }
            if(!snapshot.hasData) return Container();

            if(snapshot.data!.imageUrl.isNotEmpty) {
              return SizedBox(
              width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.width,
                child: Center(
                  child:extendedImage(snapshot.data!.imageUrl, context,200,200,cache: true, )
                  ,

                // child: Image.network(
                //  snapshot.data!.imageUrl,
                //   fit: BoxFit.cover,
                //   width: MediaQuery.of(context).size.width,
                //   height: MediaQuery.of(context).size.width,
                //
                // ),
              ),
            );
            }

             return SizedBox(
               width: MediaQuery.of(context).size.width,
               height: MediaQuery.of(context).size.width,

               child: Center(
                 child: Image.file(
                   File(snapshot.data!.image.path),
                   fit: BoxFit.cover,
                   width: MediaQuery.of(context).size.width,
                   height: MediaQuery.of(context).size.width,


                 ),
               ),
             );
          });
    }

  //endregion


  // region Drag and reorder
  Widget dragReorderText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        AppStrings.dragAndReorder,
        style: const TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 16,
          fontWeight: FontWeight.w400,
         // color: AppColors.appBlack.withOpacity(70),
        ),
      ),
    );
  }

  //endregion

  Widget imageList() {
    return StreamBuilder<bool>(
      stream: selectedImagePreviewBloc.gridViewRefreshCtrl.stream,
      builder: (context, snapshot) {
        return Center(
          child: ReorderableWrap(
            minMainAxisCount: 4,
            needsLongPressDraggable: true,

            // maxMainAxisCount: 5,
            spacing: 4,
            runSpacing: 4,
            padding: EdgeInsets.zero,
           crossAxisAlignment: WrapCrossAlignment.start,
           // crossAxisAlignment: WrapCrossAlignment,
            alignment:WrapAlignment.start,
            runAlignment: WrapAlignment.start,
              ///Reorder
            onReorder: (oldIndex, newIndex) {
              setState(() {
                 final element = selectedImagePreviewBloc.productImages.removeAt(oldIndex);
                selectedImagePreviewBloc.productImages.insert(newIndex, element);
              final data = AppConstants.multipleSelectedImage.removeAt(oldIndex);
                 AppConstants.multipleSelectedImage.insert(newIndex, data);
              });
            },
            header: [
              Consumer<AppConfigDataModel>(builder: (BuildContext context, AppConfigDataModel value, Widget? child) {
                return InkWell(
                  onTap: () {

                    // Check if image count has already reached the limit of 6
                    if (AppConstants.multipleSelectedImage.length >= value.appConfig!.productImageLimit) {
                      return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} ${value.appConfig!.productImageLimit} images", context);
                    }
                    selectedImagePreviewBloc.goBackToAddAndEditImage();
                  },
                  child: SizedBox(
                    height: 85,
                    width: 85,
                    child: Container(
                      decoration: BoxDecoration(
                          border: Border.all(color: AppColors.lightGray2,width: 2),
                          borderRadius: const BorderRadius.all(Radius.circular(0))
                      ),
                      child: const Center(
                        child: Icon(Icons.add),
                      ),
                    ),
                  ),
                );
              },

              ),
            ],
            children: selectedImagePreviewBloc.productImages.map((ProductImages file) {

              ///Local Image
              return Container(
                  height: 85,
                  width: 85,
                decoration: const BoxDecoration(
                ),
                  key: ValueKey(file),
                  child: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      InkWell(onTap: () {
                        selectedImagePreviewBloc.selectedImageCtrl.add(file);
                        // int pos = AppConstants.selectedImage.indexOf(file.image);
                        // selectedImagePreviewBloc.selectedImageCtrl.add(pos);
                      },
                          child: Center(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(Radius.circular(0)),
                                child: Image.file(
                                  File(file.image.path),
                                  fit: BoxFit.cover,
                                  // cacheWidth: 800,
                                  // cacheHeight: 800,
                                  height: double.infinity,
                                  width: double.infinity,
                                ),
                              ))),

                      Positioned(
                          top: 2,
                          right:2,
                          child: InkWell(
                              onTap: (){
                                setState(() {
                                  selectedImagePreviewBloc.productImages.remove(file);
                                  AppConstants.multipleSelectedImage.remove(file.image);

                                  if(AppConstants.multipleSelectedImage.isEmpty){
                                    return setState(() {


                                    });
                                  }
                                  selectedImagePreviewBloc.selectedImageCtrl.sink.add(selectedImagePreviewBloc.productImages.first);

                                });
                              },
                              child: SvgPicture.asset(AppImages.removeCircle)))


                    ],
                  ));
            }).toList(),
          ),
        );
      }
    );
  }

//endregion









}
