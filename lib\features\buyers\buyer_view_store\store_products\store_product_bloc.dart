import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_product_pagination.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum StoreProductListState { Loading, Success, Failed, Empty }

class StoreProductBloc {
  // region Common Variables
  BuildContext context;
  final String storeReference;
  final StoreInfo storeInfo;
  late StoreProductServices storeProductServices = StoreProductServices();
  List<Product> storeProductList = [];
  List<Product> _initialProductList = []; // Store initial product list

  bool isWarningVisible = false;
  late StoreProductPagination storeProductPagination;

  ///Product list
  // List<Product> productList = [];

  // endregion

  //region Controller
  final storeProductsCtrl = StreamController<StoreProductListState>.broadcast();

  //endregion

  // region | Constructor |
  StoreProductBloc(this.context, this.storeReference, this.storeInfo);

  // endregion

  // region Init
  void init() async {
    storeProductPagination = StoreProductPagination(context, this);
    //Get store products
    await getStoreProducts();
  }

// endregion

  //region Get Buyer Store Product Details
  Future<void> getStoreProducts({bool isRefresh = false}) async {
    try {
      // Set loading state only if not refreshing
      if (!isRefresh) {
        storeProductsCtrl.sink.add(StoreProductListState.Loading);
      }

      // Reset pagination state for fresh data
      storeProductPagination.currentApiCallStatus =
          StoreProductsPaginationState.Done;
      storeProductPagination.storeProductPaginationCtrl.sink
          .add(StoreProductsPaginationState.Done);

      //Get reference to Product data model
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      //Api call to get list of products
      var data = await storeProductServices.getBuyerStoreProduct(
          storeReference: storeReference, limit: 4, offset: 0);

      //Clear existing products and add new ones
      storeProductList.clear();
      storeProductList.addAll(data.data!);
      _initialProductList = List.from(data.data!); // Store initial state

      //Add all products in product data model
      productDataModel.addProductIntoList(products: storeProductList);

      //If buyer view
      if (AppConstants.appData.isUserView!) {
        checkWarning();
        return;
      }
      //If seller view
      else {
        //Check is it trusted or not
        checkStorePublic();
        return;
      }
    } on ApiErrorResponseMessage catch (error) {
      storeProductsCtrl.sink.add(StoreProductListState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      storeProductsCtrl.sink.add(StoreProductListState.Failed);
      return;
    }
  }

  //endregion

  //region Refresh Store Products
  Future<void> refreshStoreProducts() async {
    // Clear search if active
    if (isSearchActive) {
      isSearchActive = false;
      searchCtrl.clear();
    }

    // Refresh products with isRefresh flag
    await getStoreProducts(isRefresh: true);
  }
  //endregion

  //region Go to add product
  void goToAddProduct() {
    var screen = AddProductScreen(
      storeId: AppConstants.appData.storeId!,
      storeReference: AppConstants.appData.storeReference!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // Refresh products when returning from add product screen
      refreshStoreProducts();
    });
  }
  //endregion

  //region Check warning
  void checkWarning() {
    // Always set isWarningVisible to false to show products directly
    isWarningVisible = false;

    //If products list is empty
    if (storeProductList.isEmpty) {
      //Empty
      context.mounted
          ? storeProductsCtrl.sink.add(StoreProductListState.Empty)
          : null;
      return;
    }
    //Success
    storeProductsCtrl.sink.add(StoreProductListState.Success);
  }

  //endregion

  //region Check store public or not
  void checkStorePublic() {
    // Always set isWarningVisible to false to show products directly
    isWarningVisible = false;

    //If products list is empty
    if (storeProductList.isEmpty) {
      //Empty
      context.mounted
          ? storeProductsCtrl.sink.add(StoreProductListState.Empty)
          : null;
      return;
    }
    //Success
    storeProductsCtrl.sink.add(StoreProductListState.Success);
  }

  //endregion

  //region Go to Product View Screen
  goToProductListViewScreen(int index) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewProduct! != "1" && AppConstants.appData.isUserView!) {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    // var screen =   BuyerViewSingleProductScreen(productReference: "P1031221UXJW",productVersion: "1.0.0",);
    var screen = BuyerViewProductScreen(
      openingFrom: SearchScreenEnum.STORE,
      index: index,
      productList: storeProductList,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If product list are empty then broadcast empty
      if (storeProductList.isEmpty) {
        //Success
        storeProductsCtrl.sink.add(StoreProductListState.Success);
      }
      //Else refresh success
      else {
        return;
      }
    });
  }

  //endregion

  //region On tap go to Activation checklist
  void onTapGoToCheckList() {
    var screen = StoreDashBoardScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);

    //
    // AppConstants.storeLevelPersistentTabController.jumpToTab(0);
    // //Refresh button navigation
    // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }

  //endregion

  //region On tap show anyways
  void onTapAnyways() {
    isWarningVisible = false;
    //Success
    storeProductsCtrl.sink.add(StoreProductListState.Success);
  }

  //endregion

  //region Search functionality
  Timer? _debounceTimer;
  TextEditingController searchCtrl = TextEditingController();
  bool isSearchActive = false;

  Future<void> getStoreSearchedProducts(String query) async {
    try {
      //Make loading state
      storeProductsCtrl.sink.add(StoreProductListState.Loading);

      //Get reference to Product data model
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);

      //Api call to get searched products
      var data = await storeProductServices.searchStoreProducts(
        entityReference: storeReference,
        searchQuery: query,
        visitorReference: AppConstants.appData.userReference!,
        limit: 10,
        offset: 0,
      );

      if (data.data != null && data.data!.isNotEmpty) {
        //Add all products into store product list
        storeProductList = data.data!;
        //Add all products in product data model
        productDataModel.addProductIntoList(products: storeProductList);
        //Reset pagination state
        storeProductPagination.currentApiCallStatus =
            StoreProductsPaginationState.Done;
        //Success state
        storeProductsCtrl.sink.add(StoreProductListState.Success);
      } else {
        storeProductsCtrl.sink.add(StoreProductListState.Empty);
      }
    } catch (error) {
      storeProductsCtrl.sink.add(StoreProductListState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(error.toString(), context)
          : null;
    }
  }

  void onSearchProducts(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();

    // If query is empty, restore initial product list
    if (query.isEmpty) {
      isSearchActive = false;
      storeProductList = List.from(_initialProductList);
      storeProductsCtrl.sink.add(StoreProductListState.Success);
      return;
    }

    // Only start search if query has 3 or more characters
    if (query.length < 3) {
      return;
    }

    isSearchActive = true;
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      getStoreSearchedProducts(query);
    });
  }

  //endregion

  //region Dispose
  void dispose() {
    imageCache.clear();
    storeProductsCtrl.close();
    _debounceTimer?.cancel();
    searchCtrl.dispose();
  }
  //endregion
}
