import 'dart:io';

import 'package:swadesic/model/order_response/store_detail_in_orders.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

class SubOrder {
  int? suborderid;
  String? suborderNumber;
  String? packageNumber;
  String? displayPackageNumber;
  String? productReference;
  int? productid;
  String? productName;
  String? productDescription;
  String? productBrand;
  String? productImage;
  int? productQuantity;
  int? mrpPrice;
  int? sellingPrice;
  String? productVersion;
  String? productLatestVersion;

  // Variant information
  String? variantReference;
  String? variantVersion;
  String? variantLatestVersion;
  ProductVariant? variantDetails;

  String? suborderStatus;
  String? secondarySuborderStatus;
  String? cancelledDate;
  String? cancelledBy;
  String? cancellationReason;
  String? refundHoldReason;
  String? returnAndWarrantyDescription;
  String? confirmationDate;
  String? deliveredDate;
  String? estimatedDeliveryDate;
  bool? selfDeliveryByStore;
  String? deliveryPersonName;
  String? deliveryPersonContact;
  bool? deliveryByLogisticPartner;
  bool? deliveryBySwadesic;
  String? logisticPartner;
  String? trackingNumber;
  String? trackingLink;
  String? additionalNotes;
  String? returnReason;
  String? returnInitiateDate;
  String? returnConfirmationDate;
  String? estimatedPickupDate;
  String? returnPackageNumber;
  String? displayReturnPackageNumber;
  bool? selfReturnByStore;
  String? returnPersonName;
  String? returnPersonContact;
  bool? returnByLogisticPartner;
  String? returnPickupLogisticPartner;
  String? returnTrackingNumber;
  String? returnTrackingLink;
  String? additionalReturnNotes;
  int? returnDays;
  String? returnEstimatedPickupDate;
  String? returnEstimatedDeliveryDate;
  String? returnedDate;
  bool isSelected = false;
  StoreContactInfo? storeContactInfo;
  SuborderFeeDetails? suborderFeeDetails;
  PayoutDetails? payoutDetails;
  //Rate and review
  ReplyAndComments? replyAndComments;
  double ratingValue = 0.0;
  List<File> reviewImages = [];
  String reviewComment = "";
  List<RefundDetails>? refundDetails;
  String? returnConditionsJson;


  SubOrder(
      {this.suborderid,
        this.suborderNumber,

        this.packageNumber,
        this.displayPackageNumber,
        this.returnConfirmationDate,
        this.productReference,
        this.productid,
        this.refundHoldReason,
        this.productName,
        this.returnDays,
        this.productDescription,
        this.productBrand,
        this.productImage,
        this.productQuantity,
        this.mrpPrice,
        this.sellingPrice,
        this.productVersion,
        this.productLatestVersion,
        this.variantReference,
        this.variantVersion,
        this.variantLatestVersion,
        this.variantDetails,
        this.suborderStatus,
        this.secondarySuborderStatus,
        this.cancelledDate,
        this.cancelledBy,
        this.cancellationReason,
        this.returnAndWarrantyDescription,
        this.confirmationDate,
        this.deliveredDate,
        this.estimatedDeliveryDate,
        this.selfDeliveryByStore,
        this.deliveryPersonName,
        this.deliveryPersonContact,
        this.deliveryByLogisticPartner,
        this.deliveryBySwadesic,
        this.logisticPartner,
        this.trackingNumber,
        this.trackingLink,
        this.additionalNotes,
        this.returnReason,
        this.returnInitiateDate,
        this.estimatedPickupDate,
        this.returnPackageNumber,
        this.displayReturnPackageNumber,
        this.selfReturnByStore,
        this.returnPersonName,
        this.returnPersonContact,
        this.returnByLogisticPartner,
        this.returnPickupLogisticPartner,
        this.returnTrackingNumber,
        this.returnTrackingLink,
        this.additionalReturnNotes,
        this.storeContactInfo,
        this.suborderFeeDetails,
        this.payoutDetails,
        this.returnEstimatedPickupDate,
        this.returnEstimatedDeliveryDate,
        this.returnedDate,
        this.refundDetails,
        this.returnConditionsJson,
        //Rate and review
        // this.ratingValue,
        // this.reviewImages,
        // this.reviewComment,
      });

  SubOrder.fromJson(Map<String, dynamic> json) {
    suborderid = json['suborderid'];
    suborderNumber = json['suborder_number'];
    packageNumber = json['package_number'];
    // refundInitiateDate = json['refund_initiate_date'];
    returnConfirmationDate = json['return_confirmation_date'];
    // refundCompletionDate = json['refund_completion_date'];
    displayPackageNumber = json['display_package_number'];
    returnDays = json['return_days'];
    productReference = json['product_reference'];
    productid = json['productid'];
    refundHoldReason = json['refund_hold_reason']??"";
    productName = json['product_name'];
    productDescription = json['product_description'];
    productBrand = json['product_brand'];
    productImage = json['product_image'];
    productQuantity = json['product_quantity'];
    mrpPrice = json['mrp_price'];
    sellingPrice = json['selling_price'];
    productVersion = json['product_version'];
    productLatestVersion = json['product_latest_version'];

    // Parse variant information
    variantReference = json['variant_reference'];
    variantVersion = json['variant_version'];
    variantLatestVersion = json['variant_latest_version'];
    variantDetails = json['variant_details'] != null
        ? ProductVariant.fromJson(json['variant_details'])
        : null;

    suborderStatus = json['suborder_status'];
    secondarySuborderStatus = json['secondary_suborder_status'];
    cancelledDate = json['cancelled_date'];
    cancelledBy = json['cancelled_by'];
    cancellationReason = json['cancellation_reason'];
    returnAndWarrantyDescription = json['return_and_warranty_description'];
    confirmationDate = json['confirmation_date'];
    deliveredDate = json['delivered_date'];
    estimatedDeliveryDate = json['estimated_delivery_date'];
    selfDeliveryByStore = json['self_delivery_by_store'];
    deliveryPersonName = json['delivery_person_name'];
    deliveryPersonContact = json['delivery_person_contact'];
    deliveryByLogisticPartner = json['delivery_by_logistic_partner'];
    deliveryBySwadesic = json['delivery_by_swadesic'];
    logisticPartner = json['logistic_partner'];
    trackingNumber = json['tracking_number'];
    trackingLink = json['tracking_link'];
    additionalNotes = json['additional_notes'];
    returnReason = json['return_reason'];
    returnInitiateDate = json['return_initiate_date'];
    estimatedPickupDate = json['estimated_pickup_date'];
    returnPackageNumber = json['return_package_number'];
    displayReturnPackageNumber = json['display_return_package_number'];
    selfReturnByStore = json['self_return_by_store'];
    returnPersonName = json['return_person_name'];
    returnPersonContact = json['return_person_contact'];
    returnByLogisticPartner = json['return_by_logistic_partner'];
    returnPickupLogisticPartner = json['return_pickup_logistic_partner'];
    returnTrackingNumber = json['return_tracking_number'];
    returnTrackingLink = json['return_tracking_link'];
    additionalReturnNotes = json['additional_return_notes'];
    returnConditionsJson = json['return_conditions_json'] ?? "";
    // suborderFeeDetails = json['suborder_fee_details'];
    suborderFeeDetails = json['suborder_fee_details'] != null
    ? SuborderFeeDetails.fromJson(json['suborder_fee_details'])
    :null;
    payoutDetails = json['payout_details'] != null
    ? PayoutDetails.fromJson(json['payout_details'])
    :null;

    returnedDate = json['returned_date'];
    returnEstimatedPickupDate = json['return_estimated_pickup_date'] ?? "";
    returnEstimatedDeliveryDate = json['return_estimated_delivery_date'] ?? "";

        storeContactInfo = json['store_details'] != null
        ? StoreContactInfo.fromJson(json['store_details'])
        : null;
    if (json['refund_details'] != null) {
      refundDetails = <RefundDetails>[];
      json['refund_details'].forEach((v) {
        refundDetails!.add(RefundDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['suborderid'] = suborderid;
    data['suborder_number'] = suborderNumber;
    data['package_number'] = packageNumber;
    // data['refund_initiate_date'] = this.refundInitiateDate;
    // data['refund_completion_date'] = this.refundCompletionDate;
    data['display_package_number'] = displayPackageNumber;
    data['return_confirmation_date'] = returnConfirmationDate;
    data['return_days'] = returnDays;
    data['refund_hold_reason'] = refundHoldReason;
    data['product_reference'] = productReference;
    data['productid'] = productid;
    data['product_name'] = productName;
    data['product_description'] = productDescription;
    data['product_brand'] = productBrand;
    data['product_image'] = productImage;
    data['product_quantity'] = productQuantity;
    data['mrp_price'] = mrpPrice;
    data['selling_price'] = sellingPrice;
    data['product_version'] = productVersion;
    data['product_latest_version'] = productLatestVersion;

    // Add variant information to JSON
    data['variant_reference'] = variantReference;
    data['variant_version'] = variantVersion;
    data['variant_latest_version'] = variantLatestVersion;
    if (variantDetails != null) {
      data['variant_details'] = variantDetails!.toJson();
    }

    data['suborder_status'] = suborderStatus;
    data['secondary_suborder_status'] = secondarySuborderStatus;
    data['cancelled_date'] = cancelledDate;
    data['cancelled_by'] = cancelledBy;
    data['cancellation_reason'] = cancellationReason;
    data['return_and_warranty_description'] = returnAndWarrantyDescription;
    data['confirmation_date'] = confirmationDate;
    data['delivered_date'] = deliveredDate;
    data['estimated_delivery_date'] = estimatedDeliveryDate;
    data['self_delivery_by_store'] = selfDeliveryByStore;
    data['delivery_person_name'] = deliveryPersonName;
    data['delivery_person_contact'] = deliveryPersonContact;
    data['delivery_by_logistic_partner'] = deliveryByLogisticPartner;
    data['delivery_by_swadesic'] = deliveryBySwadesic;
    data['logistic_partner'] = logisticPartner;
    data['tracking_number'] = trackingNumber;
    data['tracking_link'] = trackingLink;
    data['additional_notes'] = additionalNotes;
    data['return_reason'] = returnReason;
    data['return_initiate_date'] = returnInitiateDate;
    data['estimated_pickup_date'] = estimatedPickupDate;
    data['return_package_number'] = returnPackageNumber;
    data['display_return_package_number'] = displayReturnPackageNumber;
    data['self_return_by_store'] = selfReturnByStore;
    data['return_person_name'] = returnPersonName;
    data['return_person_contact'] = returnPersonContact;
    data['return_by_logistic_partner'] = returnByLogisticPartner;
    data['return_pickup_logistic_partner'] = returnPickupLogisticPartner;
    data['return_tracking_number'] = returnTrackingNumber;
    data['return_tracking_link'] = returnTrackingLink;
    data['additional_return_notes'] = additionalReturnNotes;
    data['return_conditions_json'] = returnConditionsJson;
    // data['suborder_fee_details'] = this.suborderFeeDetails;

    if(suborderFeeDetails != null){
      data['suborder_fee_details'] = suborderFeeDetails!.toJson();
    }
    if(payoutDetails != null){
      data['payout_details'] = payoutDetails!.toJson();
    }

    data['returned_date'] = returnedDate;
    data['return_estimated_pickup_date'] = returnEstimatedPickupDate;
    data['return_estimated_delivery_date'] = returnEstimatedDeliveryDate;


        if (storeContactInfo != null) {
      data['store_details'] = storeContactInfo!.toJson();
    }

    if (refundDetails != null) {
      data['refund_details'] =
          refundDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}





class SuborderFeeDetails {
  int? productPrice;
  int? productLevelDeliveryFee;
  int? storeLevelDeliveryFee;

  SuborderFeeDetails(
      {this.productPrice,
        this.productLevelDeliveryFee,
        this.storeLevelDeliveryFee});

  SuborderFeeDetails.fromJson(Map<String, dynamic> json) {
    productPrice = json['product_price']??0;
    productLevelDeliveryFee = json['product_level_delivery_fee']??0;
    storeLevelDeliveryFee = json['store_level_delivery_fee']??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['product_price'] = productPrice;
    data['product_level_delivery_fee'] = productLevelDeliveryFee;
    data['store_level_delivery_fee'] = storeLevelDeliveryFee;
    return data;
  }
}


class PayoutDetails {
  String? payoutStatus;
  String? payoutReleaseDate;
  dynamic payoutAmount;

  PayoutDetails({this.payoutStatus, this.payoutReleaseDate, this.payoutAmount});

  PayoutDetails.fromJson(Map<String, dynamic> json) {
    payoutStatus = json['payout_status'];
    payoutReleaseDate = json['payout_release_date'];
    payoutAmount = json['payout_amount']??0.0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['payout_status'] = payoutStatus;
    data['payout_release_date'] = payoutReleaseDate;
    data['payout_amount'] = payoutAmount;
    return data;
  }
}

class RefundDetails {
  String? refundStatus;
  String? refundHoldReason;
  String? refundInitiateDate;
  String? refundedDate;
  String? refundedAmount;

  RefundDetails(
      {this.refundStatus,
        this.refundHoldReason,
        this.refundInitiateDate,
        this.refundedAmount,
        this.refundedDate});

  RefundDetails.fromJson(Map<String, dynamic> json) {
    refundStatus = json['refund_status'];
    refundHoldReason = json['refund_hold_reason']??"";
    refundInitiateDate = json['refund_initiate_date'];
    refundedDate = json['refunded_date'];
    refundedAmount = json['refunded_amount'] == null ?"":json['refunded_amount'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['refund_status'] = refundStatus;
    data['refund_hold_reason'] = refundHoldReason;
    data['refund_initiate_date'] = refundInitiateDate;
    data['refunded_date'] = refundedDate;
    data['refunded_amount'] = refundedAmount;
    return data;
  }
}


