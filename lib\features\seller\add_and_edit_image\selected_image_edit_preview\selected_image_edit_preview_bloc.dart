import 'dart:async';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_and_edit_image/add_and_edit_image_screen.dart';
import 'package:swadesic/features/seller/add_and_edit_image/selected_image_edit_preview/select_edit_image_screen/select_edit_image_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_Image_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/image_reorder_service/image_reorder_service.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SelectedImageEditPreviewState { Loading, Success, Failed }

class SelectedImageEditPreviewBloc {
  // region Common Variables
  BuildContext context;
  late ProductAndImageServices productAndImageServices;
  ProductImageResponse? productImageResponse;
  String? productReference;
  late SellerHideDeleteService sellerHideDeleteService;
  late var uploadFileService = UploadFileService();
  late ImageReorderService imageReorderService;
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel;
  List<XFile> selectedImages = [];

  // endregion

  //region Text Editing Controller
  TextEditingController brandNameTextCtrl = TextEditingController();

  //endregion

  //region Controller
  final selectedImageEditPreviewCtrl =
      StreamController<SelectedImageEditPreviewState>.broadcast();
  final selectedImageCtrl = StreamController<ProdImages>.broadcast();
  final gridViewRefreshCtrl = StreamController<bool>.broadcast();
  final hideAndVisibleAppBarCtrl = StreamController<bool>.broadcast();
  final sliderCtrl = StreamController<int>.broadcast();
  final PageController pageController = PageController();
  bool hideAndVisibleAppBar = true; // Start with app bar visible
  int currentImageIndex = 0;

  // New flow variables
  List<XFile> pendingImages = []; // Images waiting to be uploaded
  bool hasUnsavedChanges = false; // Track if there are unsaved changes
  bool hasReorderChanges = false; // Track if images have been reordered

  // Unified list for proper reordering (combines existing + pending)
  List<dynamic> unifiedImageList =
      []; // Contains both ProdImages and XFile objects

  //endregion

  // region | Constructor |
  SelectedImageEditPreviewBloc(this.context, this.productReference);

  // endregion

  // region Init
  void init() async {
    appConfigDataModel =
        Provider.of<AppConfigDataModel>(context, listen: false);

    ///Image reorder
    imageReorderService = ImageReorderService();
    productAndImageServices = ProductAndImageServices();
    await getProductImages();
    sellerHideDeleteService = SellerHideDeleteService();

    // Initialize with first image if available
    if (productImageResponse != null &&
        productImageResponse!.data!.isNotEmpty) {
      selectedImageCtrl.sink.add(productImageResponse!.data![0]);
      sliderCtrl.sink.add(0);
    }

    // Set initial app bar visibility to true
    hideAndVisibleAppBarCtrl.sink.add(hideAndVisibleAppBar);
  }
// endregion

  //region Get Product Images
  getProductImages() async {
    try {
      productImageResponse =
          await productAndImageServices.getOnlyProductImage(productReference!);

      // Initialize with first image if available
      if (productImageResponse != null &&
          productImageResponse!.data!.isNotEmpty) {
        selectedImageCtrl.sink.add(productImageResponse!.data![0]);
        sliderCtrl.sink.add(0);
        currentImageIndex = 0;
      }

      // Build unified list after getting product images
      buildUnifiedImageList();

      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Success);
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
    } on ApiErrorResponseMessage {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //region Get Product Images Silently (for internal use during save)
  Future<void> getProductImagesSilently() async {
    try {
      productImageResponse =
          await productAndImageServices.getOnlyProductImage(productReference!);

      // Initialize with first image if available
      if (productImageResponse != null &&
          productImageResponse!.data!.isNotEmpty) {
        selectedImageCtrl.sink.add(productImageResponse!.data![0]);
        sliderCtrl.sink.add(0);
        currentImageIndex = 0;
      }

      // Build unified list after getting product images
      buildUnifiedImageList();

      // Don't emit UI state - this is for silent refresh during save
    } on ApiErrorResponseMessage {
      // Don't show toast during save process
      throw Exception("Failed to get product images");
    } catch (error) {
      // Don't show toast during save process
      throw Exception("Failed to get product images");
    }
  }
  //endregion

//region Go to select edit image screen
  void goToSelectEditImageScreen() {
    int imageLimit = appConfigDataModel.appConfig!.productImageLimit.toInt();
    var screen = const SelectEditImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If value is not null then add images to pendingImages
      if (value != null) {
        //Temp list
        List<XFile> tempList = value;

        // Calculate total images including existing and pending
        int totalImages = productImageResponse!.data!.length +
            pendingImages.length +
            tempList.length;

        // If adding the new images exceeds the limit, show a warning and add only up to the limit
        if (totalImages > imageLimit) {
          int imagesToAdd = imageLimit -
              (productImageResponse!.data!.length + pendingImages.length);
          if (imagesToAdd > 0) {
            pendingImages.addAll(tempList.take(imagesToAdd));
          }
          CommonMethods.toastMessage(
              "${AppStrings.youCantAddMoreThen} $imageLimit images", context);
        } else {
          pendingImages.addAll(tempList);
        }

        // Mark as having unsaved changes if we have pending images
        if (pendingImages.isNotEmpty) {
          hasUnsavedChanges = true;
          buildUnifiedImageList(); // Rebuild unified list after adding pending images
          selectedImageEditPreviewCtrl.sink
              .add(SelectedImageEditPreviewState.Success);
        }
      }
    });
  }
//endregion

  // region Delete Image
  void deleteImage(int imageId) async {
    try {
      // Find the index of the image being deleted
      int deletedIndex = productImageResponse!.data!
          .indexWhere((element) => element.productimageid == imageId);

      productImageResponse!.data!
          .removeWhere((element) => element.productimageid == imageId);
      await sellerHideDeleteService.deleteImage(imageId);

      //If all the images are deleted the pop the screen
      if (productImageResponse!.data!.isEmpty) {
        Navigator.pop(context);
        return;
      }

      // Update current image selection if needed
      if (deletedIndex <= currentImageIndex && currentImageIndex > 0) {
        currentImageIndex--;
      } else if (currentImageIndex >= productImageResponse!.data!.length) {
        currentImageIndex = productImageResponse!.data!.length - 1;
      }

      // Update the selected image and slider
      if (productImageResponse!.data!.isNotEmpty) {
        selectedImageCtrl.sink
            .add(productImageResponse!.data![currentImageIndex]);
        sliderCtrl.sink.add(currentImageIndex);
        pageController.jumpToPage(currentImageIndex);
      }

      // Rebuild unified list after deletion
      buildUnifiedImageList();

      //Success
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

// endregion

  // region Start Upload
  void startUpload({required List<XFile> selectedImages}) async {
    try {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Loading);

      // Upload Multiple file
      for (int i = 0; i < selectedImages.length; i++) {
        await uploadFileService.uploadProductImage(
            filePath: selectedImages[i].path,
            url:
                "${AppConstants.baseUrl}/product/productimages/$productReference/",
            fileNameWithExtension: selectedImages.first.name,
            parameter: {});
      }
      //Clear selected images
      selectedImages.clear();
      //Get product images
      await getProductImages();
    } on ApiErrorResponseMessage catch (error) {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

// endregion

  //region Image Reorder Api Call (standalone - navigates away)
  void imageReorderApi() async {
    //region Try
    try {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Loading);
      await applyReorderChanges();

      // Reset reorder changes flag after successful save
      hasReorderChanges = false;
      hasUnsavedChanges = false;

      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Success);
      Navigator.pop(context);
    }
    //endregion
    on ApiErrorResponseMessage {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion

  //region On Change Slider
  void onChangeSlider(int index) {
    currentImageIndex = index;
    sliderCtrl.sink.add(index);
    if (unifiedImageList.isNotEmpty && index < unifiedImageList.length) {
      var imageItem = unifiedImageList[index];
      if (imageItem is ProdImages) {
        selectedImageCtrl.sink.add(imageItem);
      }
    }
  }
  //endregion

  //region On Select Small image (for existing images only)
  void onSelectSmallImage(int index) {
    if (productImageResponse != null &&
        productImageResponse!.data!.isNotEmpty &&
        index < productImageResponse!.data!.length) {
      // Find the index in unified list
      int unifiedIndex = unifiedImageList.indexWhere((item) =>
          item is ProdImages && item.productimageid == productImageResponse!.data![index].productimageid);
      if (unifiedIndex != -1) {
        pageController.jumpToPage(unifiedIndex);
        sliderCtrl.sink.add(unifiedIndex);
        selectedImageCtrl.sink.add(productImageResponse!.data![index]);
        currentImageIndex = unifiedIndex;
      }
    }
  }
  //endregion

  //region On Select Unified image (for both existing and pending images)
  void onSelectUnifiedImage(int index) {
    if (unifiedImageList.isNotEmpty && index < unifiedImageList.length) {
      pageController.jumpToPage(index);
      sliderCtrl.sink.add(index);
      currentImageIndex = index;

      var imageItem = unifiedImageList[index];
      if (imageItem is ProdImages) {
        selectedImageCtrl.sink.add(imageItem);
      }
    }
  }
  //endregion

  //region On tap image
  void onTapImage() {
    hideAndVisibleAppBar = !hideAndVisibleAppBar;
    hideAndVisibleAppBarCtrl.sink.add(hideAndVisibleAppBar);
  }
  //endregion

  //region Track reorder changes
  void markReorderChanges() {
    hasReorderChanges = true;
    hasUnsavedChanges = true;
    selectedImageEditPreviewCtrl.sink
        .add(SelectedImageEditPreviewState.Success);
  }

  // Build unified list for proper reordering
  void buildUnifiedImageList() {
    unifiedImageList.clear();

    // Add existing images
    if (productImageResponse?.data != null) {
      unifiedImageList.addAll(productImageResponse!.data!);
    }

    // Add pending images
    unifiedImageList.addAll(pendingImages);
  }

  // Apply reorder to unified list
  void reorderUnifiedList(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final item = unifiedImageList.removeAt(oldIndex);
    unifiedImageList.insert(newIndex, item);

    // Separate back into existing and pending lists
    separateUnifiedList();
    markReorderChanges();
  }

  // Separate unified list back into existing and pending
  void separateUnifiedList() {
    List<ProdImages> existingImages = [];
    List<XFile> newPendingImages = [];

    for (var item in unifiedImageList) {
      if (item is ProdImages) {
        existingImages.add(item);
      } else if (item is XFile) {
        newPendingImages.add(item);
      }
    }

    // Update the lists
    if (productImageResponse?.data != null) {
      productImageResponse!.data!.clear();
      productImageResponse!.data!.addAll(existingImages);
    }
    pendingImages.clear();
    pendingImages.addAll(newPendingImages);
  }
  //endregion

  //region Save all changes (Tick button functionality)
  void saveAllChanges() async {
    try {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Loading);

      // Step 1: Store the complete intended order structure before upload
      List<dynamic> intendedOrderStructure = [];
      for (var item in unifiedImageList) {
        if (item is ProdImages) {
          intendedOrderStructure
              .add({'type': 'existing', 'id': item.productimageid!});
        } else if (item is XFile) {
          intendedOrderStructure.add({'type': 'pending', 'path': item.path});
        }
      }

      // Step 2: Upload pending images if any
      if (pendingImages.isNotEmpty) {
        await uploadPendingImagesOnly(selectedImages: List.from(pendingImages));
        pendingImages.clear();
      }

      // Step 3: Apply reorder changes if any (after upload to include new images)
      if (hasReorderChanges) {
        await applyReorderBasedOnIntendedStructure(intendedOrderStructure);
      }

      // Clear all flags and mark as saved
      hasUnsavedChanges = false;
      hasReorderChanges = false;

      // Navigate back immediately after successful save (no need to show preview)
      Navigator.pop(context);
    } catch (error) {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  //endregion

  //region Apply reorder changes only
  Future<void> applyReorderChanges() async {
    for (int i = 0; i < productImageResponse!.data!.length; i++) {
      await imageReorderService.imageReorder(productReference!,
          productImageResponse!.data![i].productimageid!, i + 1);
    }
  }

  //region Apply reorder based on intended structure (for after upload)
  Future<void> applyReorderBasedOnIntendedStructure(
      List<dynamic> intendedOrderStructure) async {
    // Create a map of current images by their ID for quick lookup
    Map<int, ProdImages> imageMap = {};
    for (var image in productImageResponse!.data!) {
      imageMap[image.productimageid!] = image;
    }

    // Get newly uploaded images (those not in the intended structure)
    List<ProdImages> newlyUploadedImages = [];
    Set<int> existingIds = {};

    for (var item in intendedOrderStructure) {
      if (item['type'] == 'existing') {
        existingIds.add(item['id']);
      }
    }

    for (var image in productImageResponse!.data!) {
      if (!existingIds.contains(image.productimageid!)) {
        newlyUploadedImages.add(image);
      }
    }

    // Reorder productImageResponse.data based on intended structure
    List<ProdImages> reorderedImages = [];
    int newImageIndex = 0;

    for (var item in intendedOrderStructure) {
      if (item['type'] == 'existing') {
        int imageId = item['id'];
        if (imageMap.containsKey(imageId)) {
          reorderedImages.add(imageMap[imageId]!);
        }
      } else if (item['type'] == 'pending') {
        // Replace pending with newly uploaded image
        if (newImageIndex < newlyUploadedImages.length) {
          reorderedImages.add(newlyUploadedImages[newImageIndex]);
          newImageIndex++;
        }
      }
    }

    // Add any remaining newly uploaded images at the end
    while (newImageIndex < newlyUploadedImages.length) {
      reorderedImages.add(newlyUploadedImages[newImageIndex]);
      newImageIndex++;
    }

    // Update the productImageResponse with reordered data
    productImageResponse!.data!.clear();
    productImageResponse!.data!.addAll(reorderedImages);

    // Apply the reorder to server
    for (int i = 0; i < productImageResponse!.data!.length; i++) {
      await imageReorderService.imageReorder(productReference!,
          productImageResponse!.data![i].productimageid!, i + 1);
    }
  }
  //endregion

  //region Upload pending images only (without getProductImages call)
  Future<void> uploadPendingImagesOnly(
      {required List<XFile> selectedImages}) async {
    try {
      // Upload Multiple file
      for (int i = 0; i < selectedImages.length; i++) {
        await uploadFileService.uploadProductImage(
            filePath: selectedImages[i].path,
            url:
                "${AppConstants.baseUrl}/product/productimages/$productReference/",
            fileNameWithExtension: selectedImages[i].name,
            parameter: {});
      }
      //Clear selected images
      selectedImages.clear();

      // Refresh product images to get the latest data (silently)
      await getProductImagesSilently();
    } on ApiErrorResponseMessage catch (error) {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);
    } catch (error) {
      selectedImageEditPreviewCtrl.sink
          .add(SelectedImageEditPreviewState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  //endregion

  //region Handle back button with unsaved changes
  Future<bool> onWillPop() async {
    if (hasUnsavedChanges) {
      return await CommonMethods.appDialogBox(
            context: context,
            widget: OkayAndCancelDialogScreen(
              onTapFirstButton: () {
                Navigator.of(context).pop(false); // Stay on current screen
              },
              onTapSecondButton: () {
                // Clear pending images and allow back navigation
                pendingImages.clear();
                hasUnsavedChanges = false;
                hasReorderChanges = false;
                Navigator.of(context).pop(true); // Allow back navigation
              },
              previousScreenContext: context,
              isMessageVisible: true,
              message:
                  'Changes will not be saved if you go back. Do you want to continue?',
              firstButtonName: "Stay",
              secondButtonName: "Go Back",
            ),
          ) ??
          false;
    }
    return true; // Allow back navigation if no unsaved changes
  }
  //endregion

//region Dispose
  void dispose() {
    AppConstants.multipleSelectedImage.clear();
    selectedImageEditPreviewCtrl.close();
    hideAndVisibleAppBarCtrl.close();
    sliderCtrl.close();
    selectedImageCtrl.close();
    gridViewRefreshCtrl.close();
  }
//endregion
}
