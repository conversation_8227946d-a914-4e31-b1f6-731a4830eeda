import 'dart:convert';

import 'package:swadesic/model/product_variant/product_variant.dart';

class SellerOrdersDetailsResponse {
  String? message;
  List<PriceDetails>? grandTotals;
  List<PriceDetails>? totalAmountToBeReceived;
  List<PriceDetails>? amountReceivedTillNow;
  String? productDetail;
  List<GroupedCancelledOrReturnedOrder>? cancelledOrReturnedOrder;



  SellerOrdersDetailsResponse({this.message,this.productDetail,this.cancelledOrReturnedOrder,this.grandTotals});

  SellerOrdersDetailsResponse.fromJson(Map<String, dynamic> map) {
    message = map['message'];


    if (map['grand_total'] != null) {
      grandTotals = <PriceDetails>[];
      map['grand_total'].forEach((v) {
        grandTotals!.add(PriceDetails.fromJson(v));
      });
    } if (map['total_amount_to_be_received'] != null) {
      totalAmountToBeReceived = <PriceDetails>[];
      map['total_amount_to_be_received'].forEach((v) {
        totalAmountToBeReceived!.add(PriceDetails.fromJson(v));
      });
    }if (map['amount_received_till_now'] != null) {
      amountReceivedTillNow = <PriceDetails>[];
      map['amount_received_till_now'].forEach((v) {
        amountReceivedTillNow!.add(PriceDetails.fromJson(v));
      });
    }
    // productDetail = json['product_details'] ;
    productDetail = json.encode(map['product_details']);
    if (map['cancelled_or_returned_order'] != null) {
      cancelledOrReturnedOrder = <GroupedCancelledOrReturnedOrder>[];
      map['cancelled_or_returned_order'].forEach((v) {
        cancelledOrReturnedOrder!.add(GroupedCancelledOrReturnedOrder.fromJson(v));
      });
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['product_details'] = productDetail;

    if (grandTotals != null) {
      data['data'] = grandTotals!.map((v) => v.toJson()).toList();
    }
    if (totalAmountToBeReceived != null) {
      data['total_amount_to_be_received'] = totalAmountToBeReceived!.map((v) => v.toJson()).toList();
    }if (amountReceivedTillNow != null) {
      data['amount_received_till_now'] = amountReceivedTillNow!.map((v) => v.toJson()).toList();
    }
    if (cancelledOrReturnedOrder != null) {
      data['cancelled_or_returned_order'] =
          cancelledOrReturnedOrder!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}


class PriceDetails {
  String? orderBreakupItemText;
  String? orderBreakupItemSubtext;
  String? orderBreakupItemValue;

  PriceDetails(
      {this.orderBreakupItemText,
        this.orderBreakupItemSubtext,
        this.orderBreakupItemValue});

  PriceDetails.fromJson(Map<String, dynamic> json) {
    orderBreakupItemText = json['order_breakup_item_text'];
    orderBreakupItemSubtext = json['order_breakup_item_subtext'];
    orderBreakupItemValue = json['order_breakup_item_value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['order_breakup_item_text'] = orderBreakupItemText;
    data['order_breakup_item_subtext'] = orderBreakupItemSubtext;
    data['order_breakup_item_value'] = orderBreakupItemValue;
    return data;
  }
}

// class Data {
//   int? totalProductCost;
//   String? totalProductCostBreakup;
//   int? shippingFee;
//   String? shippingFeeBreakup;
//   String? productDetail;
//   List<CancelledOrReturnedOrder>? cancelledOrReturnedOrder;
//
//   Data(
//       {this.totalProductCost,
//         this.totalProductCostBreakup,
//         this.shippingFee,
//         this.shippingFeeBreakup,
//         this.productDetail,
//         this.cancelledOrReturnedOrder
//       });
//
//   Data.fromJson(Map<String, dynamic> map) {
//     totalProductCost = map['total_product_cost']??0;
//     totalProductCostBreakup = map['total_product_cost_breakup'];
//     shippingFee = map['shipping_fee'];
//     shippingFeeBreakup = map['shipping_fee_breakup'];
//     productDetail = json.encode(map['product_details']) ;
//     if (map['cancelled_or_returned_order'] != null) {
//       cancelledOrReturnedOrder = <CancelledOrReturnedOrder>[];
//       map['cancelled_or_returned_order'].forEach((v) {
//         cancelledOrReturnedOrder!.add(new CancelledOrReturnedOrder.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['total_product_cost'] = this.totalProductCost;
//     data['total_product_cost_breakup'] = this.totalProductCostBreakup;
//     data['shipping_fee'] = this.shippingFee;
//     data['shipping_fee_breakup'] = this.shippingFeeBreakup;
//     data['product_details'] = this.productDetail;
//     if (this.cancelledOrReturnedOrder != null) {
//       data['cancelled_or_returned_order'] =
//           this.cancelledOrReturnedOrder!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }




// class Group {
//   String? productReference;
//   String? productName;
//   String? productImage;
//   int? productQuantity;
//   String? productVersion;
//   String? productPrice;
//   String? status;
//   int? totalProductPrice;
//   int? productDeliveryFee;
//   int? storeLevelFee;
//   String? groupName;
//
//   Group(
//       {this.productReference,
//         this.productName,
//         this.productImage,
//         this.productQuantity,
//         this.productVersion,
//         this.productPrice,
//         this.status,
//         this.totalProductPrice,
//         this.productDeliveryFee,
//         this.storeLevelFee,
//       this.groupName
//       });
//
//   Group.fromJson(Map<String, dynamic> json) {
//     productReference = json['product_reference'];
//     productName = json['product_name'];
//     productImage = json['product_image'];
//     productQuantity = json['product_quantity'];
//     productVersion = json['product_version'];
//     productPrice = json['product_price'];
//     status = json['status'];
//     totalProductPrice = json['total_product_price'];
//     productDeliveryFee = json['product_delivery_fee'];
//     storeLevelFee = json['store_level_fee'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['product_reference'] = this.productReference;
//     data['product_name'] = this.productName;
//     data['product_image'] = this.productImage;
//     data['product_quantity'] = this.productQuantity;
//     data['product_version'] = this.productVersion;
//     data['product_price'] = this.productPrice;
//     data['status'] = this.status;
//     data['total_product_price'] = this.totalProductPrice;
//     data['product_delivery_fee'] = this.productDeliveryFee;
//     data['store_level_fee'] = this.storeLevelFee;
//     return data;
//   }
// }

class GroupedCancelledOrReturnedOrder {
  String? productReference;
  String? productName;
  String? productBrand;
  String? productImage;
  int? productQuantity;
  String? productVersion;
  String? returnDescription;
  int? returnDays;
  String? productPrice;
  String? status;
  int? totalProductPrice;
  int? productDeliveryFee;
  int? storeLevelFee;
  String? groupName;

  // Variant information
  String? variantReference;
  String? variantVersion;
  String? variantLatestVersion;
  ProductVariant? variantDetails;


  GroupedCancelledOrReturnedOrder(
      {this.productReference,
        this.productName,
        this.productBrand,
        this.productImage,
        this.returnDays,
        this.productQuantity,
        this.productVersion,
        this.returnDescription,
        this.productPrice,
        this.status,
        this.totalProductPrice,
        this.productDeliveryFee,
        this.storeLevelFee,
        this.groupName,
        this.variantReference,
        this.variantVersion,
        this.variantLatestVersion,
        this.variantDetails,
      });

  GroupedCancelledOrReturnedOrder.fromJson(Map<String, dynamic> json) {
    productReference = json['product_reference'];
    productName = json['product_name'];
    productBrand = json['product_brand'];
    productImage = json['product_image'];
    returnDays = json['return_days']??0;
    productQuantity = json['product_quantity'];
    productVersion = json['product_version'];
    returnDescription = json['return_description'];
    productPrice = json['product_price'];
    status = json['status'];
    totalProductPrice = json['total_product_price'];
    productDeliveryFee = json['product_delivery_fee'];
    storeLevelFee = json['store_level_fee'];

    // Parse variant information
    variantReference = json['variant_reference'];
    variantVersion = json['variant_version'];
    variantLatestVersion = json['variant_latest_version'];
    variantDetails = json['variant_details'] != null
        ? ProductVariant.fromJson(json['variant_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['product_reference'] = productReference;
    data['product_name'] = productName;
    data['product_brand'] = productBrand;
    data['product_image'] = productImage;
    data['return_days'] = returnDays;
    data['product_quantity'] = productQuantity;
    data['product_version'] = productVersion;
    data['return_description'] = returnDescription;
    data['product_price'] = productPrice;
    data['status'] = status;
    data['total_product_price'] = totalProductPrice;
    data['product_delivery_fee'] = productDeliveryFee;
    data['store_level_fee'] = storeLevelFee;

    // Add variant information to JSON
    data['variant_reference'] = variantReference;
    data['variant_version'] = variantVersion;
    data['variant_latest_version'] = variantLatestVersion;
    if (variantDetails != null) {
      data['variant_details'] = variantDetails!.toJson();
    }

    return data;
  }
}

