import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class AppLinkCreateService {
  ///1. Post link create
  //region Create post link
  String createPostLink({required String postReference}) {
    String inviteCode = "";
    //Main url
    String data = "d?r=$postReference";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView!) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
    }

    //Encoded
    var encodedData = CommonMethods.encodeBase32(data);

    return "${AppConstants.domainName}$encodedData";
  }
  //endregion

  ///2. Product link create
  //region Create product link
  String createProductLink({required String productReference}) {
    String inviteCode = "";
    //Main url
    String data = "d?r=$productReference";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView!) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
    }

    //Encoded
    var encodedData = CommonMethods.encodeBase32(data);

    return "${AppConstants.domainName}$encodedData";
  }
//endregion


  ///2. Product link create
  //region Create product link
  String createProductSlugLink({required String productSlug, required String storeHandle}) {
    String inviteCode = "";
    //Main url
    String data = "$storeHandle/p/$productSlug";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView!) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
    }

    //Encoded
    // var encodedData = CommonMethods.encodeBase32(data);

    return "${AppConstants.domainName}$data";
  }
//endregion


  ///3. Store link create
  //region Create store link
  String createStoreLink({required String storeHandle}) {
    String inviteCode = "";
    //Main url
    String data = "$storeHandle";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView!) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data/d?ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data/d?ic=$inviteCode";
        }
      }
    }

    //Encoded
    var encodedData = CommonMethods.encodeBase32(data);

    return "${AppConstants.domainName}$encodedData";
  }
//endregion

  ///4. External Review link create
  //region Create external review link
  String createExternalReviewLink(
      {required String token,
      required String productReference,
      required String userReference}) {
    String inviteCode = "";
    //Main url
    String data = "?t=$token&pr=$productReference&ur=$userReference";
    // If non-static user
    if (!CommonMethods().isStaticUser()) {
      //If User view
      if (AppConstants.appData.isUserView!) {
        LoggedInUserInfoDataModel userDetailDataModel =
            Provider.of<LoggedInUserInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = userDetailDataModel.userDetail!.inviteCode ?? "";
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
      //Store view
      else {
        //Seller admin data model
        SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
            Provider.of<SellerOwnStoreInfoDataModel>(
                AppConstants.currentSelectedTabContext,
                listen: false);
        inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode ?? "";
        //If inviteCode is empty then don't add
        if (inviteCode != "") {
          data = "$data&ic=$inviteCode";
        }
      }
    }

    //Encoded
    var encodedData = CommonMethods.encodeBase32(data);

    return "${AppConstants.domainName}$encodedData";
  }
  //endregion
}
