import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/gst_and_pan/gst_and_pan.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_bloc.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_common_widgets.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/trust_center_label/trust_center_label.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/digital_store.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/seller_trust_center_response/get_seller_trust_center_document_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Trust Center Screen
class SellerTrustCenterScreen extends StatefulWidget {
  final String storeRef;
  final bool isStoreOwnerView;

  const SellerTrustCenterScreen(
      {Key? key, required this.storeRef, this.isStoreOwnerView = false})
      : super(key: key);

  @override
  _SellerTrustCenterScreenState createState() =>
      _SellerTrustCenterScreenState();
}
// endregion

class _SellerTrustCenterScreenState extends State<SellerTrustCenterScreen> {
  // region Bloc
  late SellerTrustCenterBloc sellerTrustCenterBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print(widget.storeRef);
    sellerTrustCenterBloc = SellerTrustCenterBloc(context, widget.storeRef);
    sellerTrustCenterBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.trustCenter,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  // region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await sellerTrustCenterBloc.refreshTrustCenterData();
      },
      child: ListView(
        shrinkWrap: true,
        children: [
          //User view detail
          Visibility(visible: !widget.isStoreOwnerView, child: userViewInfo()),
          //Store owner view detail
          Visibility(visible: widget.isStoreOwnerView, child: sellerViewInfo()),
          // verticalSizedBox(20),
          storeInfo(),
          const SizedBox(height: 15),
          verificationStatus(),
          // verticalSizedBox(36),
          trustCenterContact(),
          trustCenterLocation(),
          idVerification(),
          addLabels(),
          AppCommonWidgets.bottomListSpace(context: context),
        ],
      ),
    );
  }

// endregion

  //region Store info
  Widget storeInfo() {
    return Container(
      margin: const EdgeInsets.only(right: 16, left: 16, top: 28),
      child: StreamBuilder<SellerTrustCenterState>(
          stream: sellerTrustCenterBloc.storeInfoCtrl.stream,
          initialData: SellerTrustCenterState.Loading,
          builder: (context, snapshot) {
            // Always show store info if data is available, regardless of stream state
            if (sellerTrustCenterBloc.singleStoreInfoResponse.data != null) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  //Icon
                  InkWell(
                    onTap: () {
                      sellerTrustCenterBloc.goToStore();
                    },
                    child: CustomImageContainer(
                      width: 50,
                      height: 50,
                      imageUrl: sellerTrustCenterBloc
                          .singleStoreInfoResponse.data!.icon,
                      imageType: CustomImageContainerType.store,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  //Handle info
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            sellerTrustCenterBloc.goToStore();
                          },
                          child: Row(
                            children: [
                              Text(
                                sellerTrustCenterBloc
                                    .singleStoreInfoResponse.data!.storehandle!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: AppTextStyle.settingHeading1(
                                        textColor: AppColors.writingBlack0)
                                    .copyWith(height: 0),
                              ),
                              VerifiedBadge(
                                width: 15,
                                height: 15,
                                subscriptionType: sellerTrustCenterBloc
                                    .singleStoreInfoResponse
                                    .data!
                                    .subscriptionType,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Text(
                          sellerTrustCenterBloc.singleStoreInfoResponse.data!
                                      .firstVerifiedDate?.isNotEmpty ==
                                  true
                              ? "Joined Swadesic: ${_formatJoinedDate(sellerTrustCenterBloc.singleStoreInfoResponse.data!.firstVerifiedDate!)}"
                              : "Store not activated",
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.writingBlack0),
                        )
                      ],
                    ),
                  ),
                  // //Report
                  // Visibility(
                  //   visible: !widget.isStoreOwnerView,
                  //   child: InkWell(
                  //     onTap: (){
                  //       sellerTrustCenterBloc.goToReportScreen();
                  //     },
                  //     child: RotatedBox(
                  //         quarterTurns: 2,
                  //         child: SvgPicture.asset(AppImages.exclamation,color: AppColors.red)),
                  //   ),
                  // )
                ],
              );
            }

            // Only show loading on initial load (when no data exists yet)
            if (snapshot.data == SellerTrustCenterState.Loading &&
                sellerTrustCenterBloc.singleStoreInfoResponse.data == null) {
              return const SizedBox();
            }

            // Show error only if explicitly failed and no data available
            if (snapshot.data == SellerTrustCenterState.Failed &&
                sellerTrustCenterBloc.singleStoreInfoResponse.data == null) {
              return errorText();
            }

            return const SizedBox();
          }),
    );
  }
  //endregion

  //region Seller View info
  Widget sellerViewInfo() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        brandAreBuiltOnTrust(),
        ensurePrivacy(),
        // moreInfoMoreTrust(),
      ],
    );
  }
  //endregion

  //region User View info
  Widget userViewInfo() {
    return Column(
      children: [
        // Container(
        //   margin: const EdgeInsets.symmetric(horizontal: 20),
        //   height: 30,
        //   child: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       Text(AppStrings.trustCenter,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
        //       horizontalSizedBox(10),
        //       SvgPicture.asset(AppImages.shield),
        //       Expanded(child: horizontalSizedBox(10)),
        //       Container(
        //         alignment: Alignment.center,
        //         padding: const EdgeInsets.symmetric(horizontal: 10),
        //         decoration: BoxDecoration(
        //           color: AppColors.lightGray,
        //           borderRadius: BorderRadius.circular(20),
        //         ),
        //         child: CupertinoButton(
        //           onPressed: (){
        //             sellerTrustCenterBloc.goToReportScreen();
        //           },
        //           padding: EdgeInsets.zero,
        //           child: Row(
        //             mainAxisSize: MainAxisSize.min,
        //             mainAxisAlignment: MainAxisAlignment.center,
        //             crossAxisAlignment: CrossAxisAlignment.center,
        //             children: [
        //               Text(AppStrings.report,style: AppTextStyle.access0(textColor: AppColors.appBlack),),
        //
        //               horizontalSizedBox(10),
        //               SvgPicture.asset(AppImages.exclamation,color: AppColors.red,),
        //
        //             ],
        //           ),
        //         ),
        //       )
        //
        //     ],
        //   ),
        // ),
        // verticalSizedBox(17),
        Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              AppStrings.swadesicIntendsToBring,
              style:
                  AppTextStyle.heading3Medium(textColor: AppColors.brandBlack),
            ))
      ],
    );
  }
  //endregion

  //region Brand are built on trust
  Widget brandAreBuiltOnTrust() {
    return Container(
      margin: const EdgeInsets.all(15),
      child: Text(AppStrings.brandAreBuiltOnTrust,
          style: AppTextStyle.subTitle(textColor: AppColors.brandBlack)),
    );
  }

//endregion

  //region Company is a member of
  Widget companyMember() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        "Nykaa online is a member of Whitelabel since November, 2020.",
        style: TextStyle(
          fontSize: 14,
          fontFamily: "LatoRegular",
          fontWeight: FontWeight.w400,
          color: AppColors.appBlack,
        ),
      ),
    );
  }

  //endregion

  //region Trust Score and Seller level
  Widget scoreLevel() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          trustScore(),
          Expanded(child: horizontalSizedBox(10)),

          ///Un-comment
          // sellerLevel(),
        ],
      ),
    );
  }

  //endregion

  //region Whitelabel trust score
  Widget trustScore() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            AppStrings.whitelabelTrustScore,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: "LatoBold",
              fontWeight: FontWeight.w700,
              color: AppColors.writingColor2,
            ),
          ),
          verticalSizedBox(10),
          const Text(
            "50/100",
            style: TextStyle(
              fontSize: 18,
              fontFamily: "LatoMedium",
              fontWeight: FontWeight.w500,
              color: AppColors.appBlack,
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Whitelabel seller level
  Widget sellerLevel() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          AppStrings.whitelabelSellerLevel,
          style: const TextStyle(
            fontSize: 12,
            fontFamily: "LatoBold",
            fontWeight: FontWeight.w700,
            color: AppColors.writingColor2,
          ),
        ),
        verticalSizedBox(10),
        const Text(
          "Silver B",
          style: TextStyle(
            fontSize: 18,
            fontFamily: "LatoMedium",
            fontWeight: FontWeight.w500,
            color: AppColors.appBlack,
          ),
        ),
      ],
    );
  }

  //endregion

  //region How to increase
  Widget howToIncrease() {
    return InkWell(
      onTap: () {
        sellerTrustCenterBloc.onTapHowToIncrease();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Text(
          AppStrings.howToIncrease,
          textAlign: TextAlign.left,
          style: const TextStyle(
              fontSize: 14,
              fontFamily: "LatoRegular",
              fontWeight: FontWeight.w400,
              color: AppColors.brandBlack,
              decoration: TextDecoration.underline),
        ),
      ),
    );
  }

  //endregion

  //region Ensure privacy
  Widget ensurePrivacy() {
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15),
      child: Text(
        AppStrings.ensurePrivacy,
        textAlign: TextAlign.left,
        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      ),
    );
  }
  //endregion

  //region More info equal more trust
  Widget moreInfoMoreTrust() {
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15),
      child: Text(
        AppStrings.moreInfoEqualMoreTrust,
        textAlign: TextAlign.left,
        style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      ),
    );
  }
  //endregion

  //region TrustCenterContact
  Widget trustCenterContact() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 36),
      child: StreamBuilder<SellerTrustCenterState>(
          stream: sellerTrustCenterBloc.trustCenterContactCtrl.stream,
          initialData: SellerTrustCenterState.Loading,
          builder: (context, snapshot) {
            if (snapshot.data == SellerTrustCenterState.Empty) {
              return Visibility(
                visible: widget.isStoreOwnerView,
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        sellerTrustCenterBloc.goToSellerContact();
                      },
                      child: SellerTrustCenterCommonWidgets.trustCenterButton(
                          buttonName: AppStrings.contact),
                    ),
                    Expanded(child: horizontalSizedBox(10))
                  ],
                ),
              );
            }
            if (snapshot.data == SellerTrustCenterState.Success) {
              return contactInformation();
            }
            if (snapshot.data == SellerTrustCenterState.Loading) {
              return Container();
            }
            return errorText();
          }),
    );
  }
  //endregion

  //region TrustCenterLocation
  Widget trustCenterLocation() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 36),
      child: StreamBuilder<SellerTrustCenterState>(
          stream: sellerTrustCenterBloc.trustCenterLocationCtrl.stream,
          initialData: SellerTrustCenterState.Loading,
          builder: (context, snapshot) {
            if (snapshot.data == SellerTrustCenterState.Empty) {
              return Visibility(
                visible: widget.isStoreOwnerView,
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        sellerTrustCenterBloc.goToSellerStoreLocation();
                      },
                      child: SellerTrustCenterCommonWidgets.trustCenterButton(
                          buttonName: AppStrings.storeLocation),
                    ),
                    Expanded(child: horizontalSizedBox(10))
                  ],
                ),
              );
            }
            if (snapshot.data == SellerTrustCenterState.Success) {
              return location();
            }
            if (snapshot.data == SellerTrustCenterState.Loading) {
              return Container();
            }
            return errorText();
          }),
    );
  }

  //endregion

  ///ID verification button and text
  //region Id verification button and text
  Widget idVerification() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 36),
      child: Column(
        children: [
          StreamBuilder<SellerTrustCenterState>(
              stream: sellerTrustCenterBloc.trustCenterLocationCtrl.stream,
              builder: (context, snapshot) {
                ///Success and store owner view
                if (snapshot.data == SellerTrustCenterState.Success) {
                  ///If Gst and pan are not null
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber !=
                          null ||
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName !=
                          null) {
                    return GstAndPanCard(
                      isSignatureVisible: false,
                      singleStoreInfoResponse:
                          sellerTrustCenterBloc.singleStoreInfoResponse,
                      isStoreOwnerView: widget.isStoreOwnerView,
                      sellerTrustCenterBloc: sellerTrustCenterBloc,
                    );
                  }

                  ///If both gst and pan are null (User view)
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber ==
                          null &&
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName ==
                          null &&
                      !widget.isStoreOwnerView) {
                    return const SizedBox();
                  }

                  ///If both gst and pan are null (Seller own store view)
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber ==
                          null &&
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName ==
                          null &&
                      widget.isStoreOwnerView) {
                    return InkWell(
                      onTap: () {
                        sellerTrustCenterBloc.goToGstVerification();
                      },
                      child: SellerTrustCenterCommonWidgets.trustCenterButton(
                          buttonName: AppStrings.idVerification),
                    );
                  }
                }

                ///Empty
                if (snapshot.data == SellerTrustCenterState.Empty) {
                  ///If Gst and pan are not null
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber !=
                          null ||
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName !=
                          null) {
                    return GstAndPanCard(
                      singleStoreInfoResponse:
                          sellerTrustCenterBloc.singleStoreInfoResponse,
                      isStoreOwnerView: widget.isStoreOwnerView,
                      sellerTrustCenterBloc: sellerTrustCenterBloc,
                    );
                  }

                  ///If both gst and pan are null (User view)
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber ==
                          null &&
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName ==
                          null &&
                      !widget.isStoreOwnerView) {
                    return const SizedBox();
                  }

                  ///If both gst and pan are null (Seller own store view)
                  if (sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.gstNumber ==
                          null &&
                      sellerTrustCenterBloc
                              .singleStoreInfoResponse.data!.panName ==
                          null &&
                      widget.isStoreOwnerView) {
                    return InkWell(
                      onTap: () {
                        sellerTrustCenterBloc.goToGstVerification();
                      },
                      child: SellerTrustCenterCommonWidgets.trustCenterButton(
                          buttonName: AppStrings.idVerification),
                    );
                  }
                }

                return const SizedBox();
              }),
          //idVerificationDisclaimer(),
        ],
      ),
    );
  }
  //endregion

  /// After Document uploaded
//region After Document upload

//region Contact information
  Widget contactInformation() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(AppStrings.contactInformation,
                textAlign: TextAlign.left,
                style:
                    AppTextStyle.sectionHeading(textColor: AppColors.appBlack)),
            Expanded(child: horizontalSizedBox(10)),
            Visibility(
              visible: widget.isStoreOwnerView,
              child: InkWell(
                onTap: () {
                  sellerTrustCenterBloc.goToSellerContact();
                },
                child: Text("change",
                    style: AppTextStyle.access0(
                        textColor: AppColors.writingBlack1)),
              ),
            )
          ],
        ),
        const SizedBox(height: 20),
        emailPhone(),
      ],
    );
  }

//endregion

//region Email and Phone
  Widget emailPhone() {
    List<String> emailList = sellerTrustCenterBloc
        .getSellerTrustCenterDetailResponse.data!.emailid!
        .split("|");
    List<String> phoneNumberList = sellerTrustCenterBloc
        .getSellerTrustCenterDetailResponse.data!.phonenumber!
        .split("|");
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Visibility(
          visible: sellerTrustCenterBloc.getSellerTrustCenterDetailResponse
                  .data!.phonenumber!.isNotEmpty &&
              sellerTrustCenterBloc.getSellerTrustCenterDetailResponse.data!
                      .phonenumber!.length >
                  10,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Phone",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              ListView.builder(
                  padding: const EdgeInsets.only(left: 10, top: 5),
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: phoneNumberList.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        CommonMethods.openDialPad(
                            phoneNumber: phoneNumberList[index]);
                      },
                      onLongPress: () {
                        CommonMethods.copyText(context, phoneNumberList[index]);
                      },
                      child: Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          phoneNumberList[index],
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                    );
                  })
            ],
          ),
        ),
        Visibility(
          visible: sellerTrustCenterBloc
              .getSellerTrustCenterDetailResponse.data!.emailid!.isNotEmpty,
          child: Container(
            margin: const EdgeInsets.only(top: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Email",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack),
                    )),
                ListView.builder(
                    padding: const EdgeInsets.only(left: 10, top: 5),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: emailList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          CommonMethods.openEmail(emailId: emailList[index]);
                        },
                        onLongPress: () {
                          CommonMethods.copyText(context, emailList[index]);
                        },
                        child: Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            emailList[index],
                            style: AppTextStyle.access0(
                                textColor: AppColors.appBlack),
                          ),
                          // child: appText(
                          //   phoneNumberList[index],
                          //   fontSize: 14,
                          //   color: AppColors.appBlack,
                          //   fontWeight: FontWeight.w400,
                          //   fontFamily: AppConstants.rRegular,
                          //   maxLine: 1,
                          // ),
                        ),
                      );
                    })
              ],
            ),
          ),
        ),
      ],
    );
  }

//endregion

//region Location
  Widget location() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(AppStrings.location,
                style:
                    AppTextStyle.sectionHeading(textColor: AppColors.appBlack)),
            Expanded(child: horizontalSizedBox(10)),
            Visibility(
              visible: widget.isStoreOwnerView,
              child: InkWell(
                onTap: () {
                  sellerTrustCenterBloc.goToSellerStoreLocation();
                },
                child: Text("change",
                    style: AppTextStyle.access0(
                        textColor: AppColors.writingColor2)),
              ),
            )
          ],
        ),
        verticalSizedBox(20),
        sellerAddress()
      ],
    );
  }

//endregion

//region Address
  Widget sellerAddress() {
    String address =
        "${sellerTrustCenterBloc.getSellerTrustCenterDetailResponse.data!.address!}, "
        "${sellerTrustCenterBloc.getSellerTrustCenterDetailResponse.data!.state}, "
        "${sellerTrustCenterBloc.getSellerTrustCenterDetailResponse.data!.city}, "
        "${sellerTrustCenterBloc.getSellerTrustCenterDetailResponse.data!.pincode}";
    return Container(
      margin: const EdgeInsets.only(left: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          InkWell(
            onLongPress: () {
              CommonMethods.copyText(context, address);
            },
            child: Text(
              address,
              maxLines: 2,
              textAlign: TextAlign.left,
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
          ),
          //Physical
          Visibility(
              visible: !sellerTrustCenterBloc
                  .getSellerTrustCenterDetailResponse.data!.isphysicalstore!,
              child: Container(
                  margin: const EdgeInsets.only(top: 15),
                  child: DigitalStoreWidget(
                    isVisible: true,
                    message: "This is a Digital only store",
                  ))),
          Visibility(
              visible: sellerTrustCenterBloc
                  .getSellerTrustCenterDetailResponse.data!.isphysicalstore!,
              child: Container(
                  margin: const EdgeInsets.only(top: 15),
                  child: DigitalStoreWidget(
                    isVisible: true,
                    message: "This is a Physical and Digital store",
                  )))
        ],
      ),
    );
  }

//endregion

//region Seller address Map
  Widget sellerAddressMap() {
    return Container(
      height: 220,
      color: AppColors.red,
    );
  }

//endregion

//region Documents submitted
  Widget documentSubmitted() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(20),
        Row(
          children: [
            Text(
              AppStrings.documentsSubmitted,
              style: const TextStyle(
                fontSize: 15,
                fontFamily: "LatoSemiBold",
                fontWeight: FontWeight.w600,
                color: AppColors.writingColor2,
              ),
            ),
            Expanded(child: horizontalSizedBox(10)),
            Visibility(
              visible: widget.isStoreOwnerView,
              child: InkWell(
                onTap: () {
                  sellerTrustCenterBloc.goToSellerDocument();
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  decoration: const BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.all(Radius.circular(20))),
                  child: const Text("change",
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: "LatoSemiBold",
                        fontWeight: FontWeight.w600,
                        color: AppColors.writingColor3,
                      )),
                ),
              ),
            )
          ],
        ),
        verticalSizedBox(15),
        documents(),
      ],
    );
  }

//endregion

//region Documents
  Widget documents() {
    int selectedDoc = 0;
    return SizedBox(
      height: 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: sellerTrustCenterBloc
            .getSellerTrustCenterDocumentResponse.documentList!.length,
        itemBuilder: (BuildContext, index) {
          return StreamBuilder<bool>(
              stream: sellerTrustCenterBloc.loadingDocumentCtrl.stream,
              builder: (context, snapshot) {
                //Owner view
                if (widget.isStoreOwnerView) {
                  return ownerView(
                      document: sellerTrustCenterBloc
                          .getSellerTrustCenterDocumentResponse
                          .documentList![index]);
                }
                //Public view
                else {
                  return publicView(
                      document: sellerTrustCenterBloc
                          .getSellerTrustCenterDocumentResponse
                          .documentList![index]);
                }
              });
        },
      ),
    );
  }
//endregion

//endregion

//region Owner view document
  Widget ownerView({required Document document}) {
    return InkWell(
      onTap: () {
        sellerTrustCenterBloc.startDownload(selectedDocument: document);
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  AppImages.documentPlaceHolder,
                  fit: BoxFit.cover,
                ),
                verticalSizedBox(5),
                Text(
                  document.documentname!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: "LatoRegular",
                    fontSize: 12,
                    color: AppColors.appBlack,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
//endregion

//region Public view document
  Widget publicView({required Document document}) {
    return InkWell(
      onTap: () {
        //If document is public
        if (document.showToPublic!) {
          sellerTrustCenterBloc.startDownload(selectedDocument: document);
        }
        //If document is private
        else {
          CommonMethods.toastMessage(AppStrings.notAbleToOpen, context);
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    SvgPicture.asset(
                      AppImages.documentPlaceHolder,
                      fit: BoxFit.cover,
                    ),
                    Visibility(
                      visible: !document.showToPublic!,
                      child: const Positioned(
                          top: 5, right: 5, child: Icon(Icons.lock)),
                    )
                  ],
                ),
                verticalSizedBox(5),
                Text(
                  document.documentname!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: "LatoRegular",
                    fontSize: 12,
                    color: AppColors.appBlack,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
//endregion

//region Add labels
  Widget addLabels() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 36),
      child: StreamBuilder<SellerTrustCenterState>(
          stream: sellerTrustCenterBloc.trustCenterLocationCtrl.stream,
          builder: (context, snapshot) {
            //If success
            if (snapshot.data == SellerTrustCenterState.Success) {
              return TrustCenterLabel(
                getSellerTrustCenterDetailResponse:
                    sellerTrustCenterBloc.getSellerTrustCenterDetailResponse,
                isStoreOwnerView: widget.isStoreOwnerView,
                storeReference: widget.storeRef,
              );
            }
            //If Empty
            if (snapshot.data == SellerTrustCenterState.Empty) {
              return TrustCenterLabel(
                getSellerTrustCenterDetailResponse:
                    sellerTrustCenterBloc.getSellerTrustCenterDetailResponse,
                isStoreOwnerView: widget.isStoreOwnerView,
                storeReference: widget.storeRef,
              );
            }
            return const SizedBox();
          }),
    );
  }
//endregion

//region Id verification disclaimer
  Widget idVerificationDisclaimer() {
    //Retrieve data in Seller own store data model
    // var sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
    return Consumer<SellerOwnStoreInfoDataModel>(
      builder: (BuildContext context, SellerOwnStoreInfoDataModel value,
          Widget? child) {
        return Container(
          margin: const EdgeInsets.only(top: 28),
          child: StreamBuilder<SellerTrustCenterState>(
              stream: sellerTrustCenterBloc.trustCenterLocationCtrl.stream,
              initialData: SellerTrustCenterState.Loading,
              builder: (context, snapshot) {
                //Loading
                if (snapshot.data == SellerTrustCenterState.Loading) {
                  return const SizedBox();
                }
                //Make it visible only if
                //1. Store owner view and isVerificationCompleted is false
                return Visibility(
                    visible: widget.isStoreOwnerView &&
                        !value.storeInfo!.isVerificationCompleted!,
                    child: Text(
                      "ID Verification enables Swadesic Ordering. Verification typically takes less than 24 hours. ",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.brandBlack),
                    ));
              }),
        );
      },
    );
  }
//endregion

  String _formatJoinedDate(String date) {
    if (date.isEmpty) return "";
    try {
      DateTime dateTime = DateTime.parse(date);
      List<String> months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      return "${months[dateTime.month - 1]}, ${dateTime.year}";
    } catch (e) {
      return "";
    }
  }

  //region Verification Status
  Widget verificationStatus() {
    return StreamBuilder<SellerTrustCenterState>(
      stream: sellerTrustCenterBloc.storeInfoCtrl.stream,
      builder: (context, snapshot) {
        final store = sellerTrustCenterBloc.singleStoreInfoResponse.data;

        // Always show verification status if store data is available
        if (store != null) {
          String verificationText = AppStrings.unverifiedSeller;
          bool isVerified = false;
          bool isRequested = false;
          String verificationBadge = AppImages.verificationBadgeGrey;
          String? typeIcon;

          if (store.verificationType == 'BUSINESS') {
            if (store.isGstVerified == true &&
                store.isVerificationCompleted == true) {
              verificationText = AppStrings.verifiedAsBusiness;
              verificationBadge = AppImages.verificationBadgeGreen;
              typeIcon = AppImages.verificationBadgeBusiness;
              isVerified = true;
            } else if (store.isVerificationCompleted == false) {
              verificationText = AppStrings.verificationRequestedAsBusiness;
              verificationBadge = AppImages.verificationBadgeBlack;
              typeIcon = AppImages.verificationBadgeBusiness;
              isRequested = true;
            }
          } else if (store.verificationType == 'INDIVIDUAL') {
            if (store.isPanVerified == true &&
                store.isVerificationCompleted == true) {
              verificationText = AppStrings.verifiedAsIndependentSeller;
              verificationBadge = AppImages.verificationBadgeGreen;
              typeIcon = AppImages.verificationBadgeIndividual;
              isVerified = true;
            } else if (store.isVerificationCompleted == false) {
              verificationText =
                  AppStrings.verificationRequestedAsIndependentSeller;
              verificationBadge = AppImages.verificationBadgeBlack;
              typeIcon = AppImages.verificationBadgeIndividual;
              isRequested = true;
            }
          } else {
            verificationText = AppStrings.unverifiedSeller;
          }

          return Align(
            alignment: Alignment.centerLeft,
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: isVerified
                        ? AppColors.brandBlack
                        : isRequested
                            ? Colors.black
                            : Colors.grey[400] ?? Colors.grey,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      verificationBadge,
                      width: 22,
                      height: 22,
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        verificationText,
                        style: AppTextStyle.smallText(
                          textColor: isVerified
                              ? AppColors.brandBlack
                              : isRequested
                                  ? Colors.black
                                  : Colors.grey[600] ?? Colors.grey,
                        ),
                      ),
                    ),
                    if (typeIcon != null) ...[
                      const SizedBox(width: 4),
                      SvgPicture.asset(
                        typeIcon,
                        width: 20,
                        height: 20,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        }

        // Only show loading on initial load (when no data exists yet)
        if (snapshot.data == SellerTrustCenterState.Loading && store == null) {
          return const SizedBox();
        }

        // Show error only if explicitly failed and no data available
        if (snapshot.data == SellerTrustCenterState.Failed && store == null) {
          return errorText();
        }

        return const SizedBox();
      },
    );
  }
  //endregion

  //region Error Text
  Widget errorText() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        AppStrings.commonErrorMessage,
        style: AppTextStyle.contentText0(textColor: AppColors.red),
        textAlign: TextAlign.center,
      ),
    );
  }
  //endregion
}
